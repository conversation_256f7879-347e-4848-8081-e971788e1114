# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Development
- `npm run dev` - Start development server (runs server.js)
- `npm run start` - Start production server

### Building
- `npm run build` - Build for web deployment
- `npm run build:static` - Build static export for offline use
- `npm run build:electron` - Build for Electron desktop app
- `npm run build:mobile` - Build for Capacitor mobile apps
- `npm run build:secure` - Secure build without sourcemaps

### Code Quality
- `npm run lint` - Run ESLint (configured to ignore errors during builds)
- TypeScript checking is handled automatically by Next.js (configured to ignore build errors)

### Testing
No formal test framework is configured. Testing is done through:
- `npm run test:db-flow` - Test database connectivity
- Manual testing via debug pages at `/debug/*`

### Platform-Specific Development
- `npm run electron:dev` - Develop Electron app
- `npm run cap:dev:android` - Develop Android app
- `npm run cap:dev:ios` - Develop iOS app

## Architecture Overview

### Application Structure
This is a **multi-platform restaurant management system** built with:
- **Next.js 15** (React 19) - Web framework with App Router
- **Electron** - Desktop application wrapper
- **Capacitor** - Mobile app wrapper (iOS/Android)
- **MongoDB** & **PouchDB** - Database layer with offline-first sync
- **TypeScript** - Primary language (strict mode disabled)

### Key Directories
- `/app/` - Next.js App Router pages and API routes
- `/components/` - Reusable React components
- `/lib/` - Core business logic, services, and utilities
- `/electron/` - Electron-specific code and build resources
- `/android/` & `/ios/` - Mobile platform configurations

### Multi-Platform Build System
The codebase uses `BUILD_TARGET` environment variable to generate different builds:
- `web` - Landing page only (excludes restaurant functionality)
- `static` - Full offline-capable build for Electron
- `electron` - Desktop application
- `mobile` - Capacitor mobile apps

Build-specific webpack aliases in `next.config.ts` replace server-dependent modules with empty stubs for static builds.

### Database Architecture
**Offline-First Design:**
- **PouchDB** - Client-side database for offline operation
- **MongoDB** - Server database for data persistence
- **CouchDB** - Used by Electron for local data storage
- Automatic sync between local and remote databases

### Authentication System
Multi-user authentication supporting:
- **Staff authentication** with role-based permissions
- **Restaurant owner** management
- **Session-based** authentication with JWT tokens
- Permission system for different user roles (kitchen, waiter, admin, etc.)

### Core Features
- **Order Management** - POS system with table management
- **Kitchen Display** - Real-time order tracking for kitchen staff
- **Staff Management** - Employee scheduling and payment tracking
- **Inventory Management** - Stock tracking with waste management
- **Financial Reporting** - Daily snapshots and analytics
- **Printing System** - Receipt and kitchen order printing
- **P2P Sync** - Local network synchronization between devices

### Code Conventions
- **No comments** in code unless explicitly requested
- **TypeScript** with relaxed type checking (strict: false)
- **Tailwind CSS** for styling with shadcn/ui components
- **Absolute imports** using `@/*` path mapping
- **Mobile-first** responsive design approach

### Development Patterns
- Components use **React Hook Form** with Zod validation
- Database operations via **custom hooks** (useOrderV4, useStaffV4, etc.)
- **Context providers** for shared state management
- **Error boundaries** and offline handling throughout the app

### Important Notes
- Source maps are disabled in production for security
- React Strict Mode is disabled to reduce hydration issues
- Build system handles Node.js module exclusion for static builds
- Cursor rules are configured in `.cursor/rules/` for development workflow

### Network Architecture
- **mDNS discovery** for local device detection
- **P2P synchronization** between restaurant devices
- **Offline-first** operation with background sync
- **Express server** for API endpoints and static serving

When working on this codebase, always consider the multi-platform nature and offline-first requirements. Check existing patterns in similar components before implementing new features.



IN YOUR OUTPUT  DONT NEVER TALK IN LENGHT IN FACT DONT TALK AT ALL TELLING ME WHAT U DID OR WHAT UR GOING TO DO UNLESS I ASK TO EXPLAIN OR U FEEL THE ABSOLUTE NEED TO OR U NEED CLARIFICATION WE DO THIS TO SAVE USAGE OF TOKENS IF U HAVE TO RECAP U MUST EXTREMELY SUMMARIZE AND TLDR


dont npm run build or dev unless i ask