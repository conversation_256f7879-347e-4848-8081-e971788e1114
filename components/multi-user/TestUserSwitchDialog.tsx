'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { UserPlus } from 'lucide-react';

interface TestUserSwitchDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function TestUserSwitchDialog({ open, onOpenChange }: TestUserSwitchDialogProps) {
  console.log('🔍 TestUserSwitchDialog render:', { open });

  const handleTestClick = () => {
    console.log('🔄 Test button clicked!');
    alert('Test button works!');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Test Dialog</DialogTitle>
          <DialogDescription>
            This is a test dialog to see if buttons work.
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col gap-3 py-4">
          <Button 
            onClick={handleTestClick}
            className="flex items-center gap-2"
          >
            <UserPlus className="h-4 w-4" />
            Test Button
          </Button>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}