import { spawn, ChildProcess } from 'child_process';
import { app } from 'electron';
import * as path from 'path';
import * as fs from 'fs';
import * as crypto from 'crypto';
import * as os from 'os';
import axios from 'axios';

interface CouchDBConfig {
  port: number;
  dataDir: string;
  adminUser: string;
  adminPassword: string;
  logLevel: string;
}

export class CouchDBServiceManager {
  private process: ChildProcess | null = null;
  private config: CouchDBConfig | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private restartAttempts = 0;
  private maxRestartAttempts = 5;
  private isShuttingDown = false;
  private resolvedCouchDBPath: string = '';
  private resolvedExecutable: string = '';

  async start(): Promise<CouchDBConfig> {
    if (this.process) {
      throw new Error('CouchDB is already running');
    }

    try {
      await this.validateBundle();
      this.config = await this.generateConfig();
      await this.setupDirectories();
      await this.generateConfigFiles();
      await this.startProcess();
      await this.waitForHealth();
      await this.createSystemDatabases();
      this.startHealthMonitoring();
      
      return this.config;
    } catch (error) {
      await this.cleanup();
      throw error;
    }
  }

  async stop(): Promise<void> {
    this.isShuttingDown = true;
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    if (this.process) {
      return new Promise<void>((resolve) => {
        const timeout = setTimeout(() => {
          if (this.process && !this.process.killed) {
            this.process.kill('SIGKILL');
          }
          this.process = null;
          resolve();
        }, 10000);

        this.process!.once('exit', () => {
          clearTimeout(timeout);
          this.process = null;
          resolve();
        });

        this.process!.kill('SIGTERM');
      });
    }
  }

  getConfig(): CouchDBConfig | null {
    return this.config;
  }

  isRunning(): boolean {
    return this.process !== null && !this.process.killed;
  }

  private async validateBundle(): Promise<void> {
    const resourcesPath = this.getResourcesPath();
    const platform = os.platform();
    
    // 🚀 Universal CouchDB path resolution (matching old working code)
    const conventionalDir = platform === 'darwin'
      ? 'couchdb-macos'
      : platform === 'win32'
        ? 'couchdb-windows'
        : 'couchdb-linux';

    let couchdbPath = path.join(resourcesPath, conventionalDir);

    const pathExists = (p: string) => {
      try { return fs.existsSync(p); } catch { return false; }
    };

    if (!pathExists(couchdbPath)) {
      // 🔍 Scan resourcesBasePath for any directory starting with "couchdb"
      const match = fs.readdirSync(resourcesPath)
        .find(d => fs.statSync(path.join(resourcesPath, d)).isDirectory() && d.toLowerCase().startsWith('couchdb'));
      if (match) {
        couchdbPath = path.join(resourcesPath, match);
      }
    }

    // Dev fallback (unpacked run)
    if (!pathExists(couchdbPath)) {
      const fallbackPath = path.resolve(__dirname, '../../resources');
      const match = fs.readdirSync(fallbackPath)
        .find(d => fs.statSync(path.join(fallbackPath, d)).isDirectory() && d.toLowerCase().startsWith('couchdb'));
      if (match) {
        couchdbPath = path.join(fallbackPath, match);
      }
    }

    if (!pathExists(couchdbPath)) {
      const err = `❗ Embedded CouchDB directory not found inside resources. Looked for '${conventionalDir}' and scanned for *couchdb* folders.`;
      console.error(`[CouchDBServiceManager] ${err}`);
      throw new Error(err);
    }

    // 👉 CRITICAL WINDOWS FIX: On Windows, CouchDB batch scripts fail when ROOTDIR contains spaces
    if (platform === 'win32' && couchdbPath.includes(' ')) {
      const portableCouchPath = path.join(app.getPath('userData'), 'couchdb-portable');

      try {
        if (!fs.existsSync(portableCouchPath)) {
          console.log(`[CouchDBServiceManager] ⚠️ Detected space in CouchDB path. Copying binaries to safe location: ${portableCouchPath}`);

          // Node 16+: fs.cpSync supports recursive directory copy
          fs.cpSync(couchdbPath, portableCouchPath, { recursive: true });
        }

        couchdbPath = portableCouchPath; // Always launch from space-free directory
      } catch (copyErr) {
        console.error('[CouchDBServiceManager] ❌ Failed to copy CouchDB binaries to portable location:', copyErr);
        // Fallback to original path (may still work on some systems)
      }
    }

    // Store the resolved path for later use
    this.resolvedCouchDBPath = couchdbPath;

    const erlPath = this.findErlExecutable(couchdbPath);
    if (!fs.existsSync(erlPath)) {
      throw new Error(`erl executable not found in erts directory for ${platform}`);
    }

    // ----------------------------------------------------------------
    // 🔑 Executable resolution – support .cmd, .bat, and .exe on Windows
    // ----------------------------------------------------------------
    let couchDbExecutable: string;
    
    if (platform === 'darwin' || platform === 'linux') {
      couchDbExecutable = path.join(couchdbPath, 'bin', 'couchdb');
    } else {
      // try multiple common filenames in order of preference
      const candidates = [
        path.join(couchdbPath, 'bin', 'couchdb.cmd'),
        path.join(couchdbPath, 'bin', 'couchdb.bat'),
        path.join(couchdbPath, 'bin', 'couchdb.exe')
      ];
      couchDbExecutable = candidates.find(pathExists) || candidates[0];
    }
    
    // 🚨 Enhanced validation for macOS embedded CouchDB
    if (!fs.existsSync(couchDbExecutable)) {
      const errMsg = `[CouchDBServiceManager] CouchDB executable not found at ${couchDbExecutable}. ` +
                     'The bundled CouchDB resources may be missing or incorrectly packaged.';
      console.error(errMsg);
      
      // For macOS, provide specific guidance
      if (platform === 'darwin') {
        console.error('[macOS] Ensure couchdb-macos bundle is present in electron/resources/');
        console.error('[macOS] Run: npm run download:couchdb-macos to prepare bundle');
      }
      
      throw new Error(errMsg);
    }
    
    // Additional macOS validation - check critical dependencies
    if (platform === 'darwin') {
      const erlangDirName = fs.readdirSync(couchdbPath).find((d) => d.startsWith('erts-'));
      if (!erlangDirName) {
        throw new Error(`[macOS] ERTS directory not found in CouchDB bundle at ${couchdbPath}`);
      }
      
      const defaultIniPath = path.join(couchdbPath, 'etc', 'default.ini');
      if (!fs.existsSync(defaultIniPath)) {
        throw new Error(`[macOS] default.ini missing at ${defaultIniPath} - incomplete CouchDB bundle`);
      }
      
      console.log(`[macOS] Bundle validation passed - ERTS: ${erlangDirName}, executable: ${path.basename(couchDbExecutable)}`);
    }

    // Store the resolved executable for later use
    this.resolvedExecutable = couchDbExecutable;
  }

  private findErlExecutable(couchdbPath: string): string {
    const ertsDir = fs.readdirSync(couchdbPath).find(dir => dir.startsWith('erts-'));
    if (!ertsDir) {
      throw new Error('ERTS directory not found');
    }
    const platform = os.platform();
    const erlExecutable = platform === 'win32' ? 'erl.exe' : 'erl';
    return path.join(couchdbPath, ertsDir, 'bin', erlExecutable);
  }

  private async generateConfig(): Promise<CouchDBConfig> {
    const port = await this.findAvailablePort();
    const dataDir = path.join(app.getPath('userData'), 'couchdb');
    const adminUser = 'admin';
    const adminPassword = 'admin'; // Use fixed password for simplicity

    return {
      port,
      dataDir,
      adminUser,
      adminPassword,
      logLevel: 'info'
    };
  }

  private async findAvailablePort(): Promise<number> {
    const net = require('net');
    
    for (let port = 5984; port <= 5994; port++) {
      const isAvailable = await new Promise<boolean>((resolve) => {
        const server = net.createServer();
        server.listen(port, () => {
          server.close(() => resolve(true));
        });
        server.on('error', () => resolve(false));
      });
      
      if (isAvailable) {
        return port;
      }
    }
    
    throw new Error('No available ports found for CouchDB');
  }

  private async setupDirectories(): Promise<void> {
    if (!this.config) throw new Error('Config not initialized');

    const dirs = [
      this.config.dataDir,
      path.join(this.config.dataDir, 'data'),
      path.join(this.config.dataDir, 'etc'),
      path.join(this.config.dataDir, 'log')
    ];

    for (const dir of dirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    }
  }

  private async generateConfigFiles(): Promise<void> {
    if (!this.config) throw new Error('Config not initialized');

    const platform = os.platform();
    const couchdbPath = this.resolvedCouchDBPath;
    const dataPath = this.config.dataDir.replace(/\\/g, '/');

    // Generate unique device ID matching old working code approach
    const deviceId = crypto.randomUUID();
    
    // 🔧 DEV MODE: Use unique node name to prevent conflicts
    const isDev = process.env.NODE_ENV === 'development' && process.env.ELECTRON_FORCE_STATIC !== 'true';
    const nodeNameSuffix = isDev ? `-dev-${Date.now()}` : '';
    const nodeName = `couchdb@127.0.0.1${nodeNameSuffix}`;

    // Basic CouchDB configuration (matching old working code structure)
    const localIni = `
[couchdb]
uuid = ${deviceId}
database_dir = ${dataPath}/data
view_index_dir = ${dataPath}/data
uri_file = ${path.join(this.config.dataDir, 'couch.uri').replace(/\\/g, '/')}
single_node = true

[log]
file = ${dataPath}/log/couchdb.log
level = ${this.config.logLevel}

[chttpd]
port = ${this.config.port}
bind_address = 0.0.0.0
enable_cors = true

[cors]
origins = *
credentials = true
methods = GET, PUT, POST, HEAD, DELETE
headers = accept, authorization, content-type, origin, referer, x-csrf-token

[couch_httpd_auth]
require_valid_user = false

[admins]
${this.config.adminUser} = ${this.config.adminPassword}

[vm_args]
-name ${nodeName}
`;

    // Enhanced macOS config handling - support both dev and release modes
    if (platform !== 'win32') {
      // For macOS, prioritize bundled CouchDB directory for config
      const localDPath = path.join(couchdbPath, 'etc', 'local.d');
      if (!fs.existsSync(localDPath)) {
        fs.mkdirSync(localDPath, { recursive: true });
      }
      const couchdbConfigPath = path.join(localDPath, '20-embedded.ini');
      
      // Also create fallback config in userData for dev mode
      const userConfigPath = path.join(this.config.dataDir, 'etc', 'local.ini');
      if (!fs.existsSync(path.dirname(userConfigPath))) {
        fs.mkdirSync(path.dirname(userConfigPath), { recursive: true });
      }
      
      // Write config to both locations for maximum compatibility
      fs.writeFileSync(couchdbConfigPath, localIni.trim());
      fs.writeFileSync(userConfigPath, localIni.trim());
      
      console.log(`[macOS] Wrote CouchDB config to: ${couchdbConfigPath}`);
      console.log(`[macOS] Wrote fallback config to: ${userConfigPath}`);
    } else {
      // For Windows, write to userData directory
      const configPath = path.join(this.config.dataDir, 'etc', 'local.ini');
      fs.writeFileSync(configPath, localIni.trim());
      console.log(`Wrote Windows CouchDB config to: ${configPath}`);
    }
  }

  private async startProcess(): Promise<void> {
    if (!this.config) throw new Error('Config not initialized');

    const platform = os.platform();
    const couchdbPath = this.resolvedCouchDBPath;
    const executable = this.resolvedExecutable;

    // Enhanced permission handling for macOS embedded CouchDB
    if (platform !== 'win32') {
      try {
        // Set execute permissions on main executable
        fs.chmodSync(executable, 0o755);
        
        // Also ensure Erlang runtime has execute permissions
        const erlangDirName = fs.readdirSync(couchdbPath).find((d) => d.startsWith('erts-')) || 'erts';
        const erlangBinDir = path.join(couchdbPath, erlangDirName, 'bin');
        
        if (fs.existsSync(erlangBinDir)) {
          const erlExecutable = path.join(erlangBinDir, 'erl');
          const beamExecutable = path.join(erlangBinDir, 'beam.smp');
          
          if (fs.existsSync(erlExecutable)) fs.chmodSync(erlExecutable, 0o755);
          if (fs.existsSync(beamExecutable)) fs.chmodSync(beamExecutable, 0o755);
          
          console.log(`[macOS] Set execute permissions on Erlang runtime: ${erlangBinDir}`);
        }
      } catch (permErr: any) {
        if (permErr.code !== 'EROFS' && permErr.code !== 'EPERM') {
          console.warn('[CouchDBServiceManager] chmod failed, continuing anyway:', permErr.message);
        } else {
          console.log('[CouchDBServiceManager] Read-only filesystem detected, skipping chmod (expected on DMG)');
        }
      }
    }

    // Setup environment variables for Windows CouchDB
    let spawnEnv = { ...process.env };
    let spawnArgs: string[] = [];
    let spawnOptions: any = {
      stdio: ['ignore', 'pipe', 'pipe'],
    };
    
    if (platform === 'win32') {
       // 🪟 Rock-solid Windows portable CouchDB setup
       // Dynamically locate bundled Erlang dir (erts-*)
       const erlangDirName = fs.readdirSync(couchdbPath).find((d) => d.startsWith('erts-')) || 'erts';
       const erlangHomeDir = path.join(couchdbPath, erlangDirName);
       const erlangBinDir = path.join(erlangHomeDir, 'bin');
       const couchBinDir = path.join(couchdbPath, 'bin');

       // Environment expected by couchdb.cmd
       spawnEnv.ROOTDIR = couchdbPath;
       spawnEnv.COUCHDB_BIN_DIR = couchBinDir;
       spawnEnv.COUCHDB_LIB_DIR = path.join(couchdbPath, 'lib');
       spawnEnv.COUCHDB_ETC_DIR = path.join(couchdbPath, 'etc');
       spawnEnv.COUCHDB_QUERY_SERVER_JAVASCRIPT = './bin/couchjs ./share/server/main.js';
       spawnEnv.COUCHDB_QUERY_SERVER_COFFEESCRIPT = './bin/couchjs ./share/server/main-coffee.js';
       spawnEnv.COUCHDB_FAUXTON_DOCROOT = './share/www';
       spawnEnv.ERLANG_HOME = erlangHomeDir;

       // Ensure Erlang & CouchDB bins are early in PATH
       const currentPath = spawnEnv.PATH || process.env.PATH || '';
       spawnEnv.PATH = `${erlangBinDir};${couchBinDir};${currentPath}`;

       // Pass default.ini THEN our local.ini so overrides win
       spawnArgs = ['-couch_ini', path.join(couchdbPath, 'etc', 'default.ini'), path.join(this.config.dataDir, 'etc', 'local.ini')];
       spawnOptions.cwd = couchdbPath;
       spawnOptions.shell = true; // .cmd requires shell on Windows
    } else {
      // 🍎/🐧 macOS & Linux setup - Enhanced for embedded CouchDB
      const erlangDirName = fs.readdirSync(couchdbPath).find((d) => d.startsWith('erts-')) || 'erts';
      const erlangHomeDir = path.join(couchdbPath, erlangDirName);
      const erlangBinDir = path.join(erlangHomeDir, 'bin');
      const couchBinDir = path.join(couchdbPath, 'bin');

      // Set essential environment variables for embedded CouchDB
      spawnEnv.ROOTDIR = couchdbPath;
      spawnEnv.COUCHDB_BIN_DIR = couchBinDir;
      spawnEnv.COUCHDB_LIB_DIR = path.join(couchdbPath, 'lib');
      spawnEnv.COUCHDB_ETC_DIR = path.join(couchdbPath, 'etc');
      spawnEnv.COUCHDB_QUERY_SERVER_JAVASCRIPT = path.join(couchdbPath, 'bin', 'couchjs') + ' ' + path.join(couchdbPath, 'share', 'server', 'main.js');
      spawnEnv.COUCHDB_QUERY_SERVER_COFFEESCRIPT = path.join(couchdbPath, 'bin', 'couchjs') + ' ' + path.join(couchdbPath, 'share', 'server', 'main-coffee.js');
      spawnEnv.COUCHDB_FAUXTON_DOCROOT = path.join(couchdbPath, 'share', 'www');
      spawnEnv.ERLANG_HOME = erlangHomeDir;

      // Ensure Erlang & CouchDB bins are in PATH for embedded execution
      const currentPath = spawnEnv.PATH || process.env.PATH || '';
      spawnEnv.PATH = `${erlangBinDir}:${couchBinDir}:${currentPath}`;

      // Enhanced macOS debugging
      if (platform === 'darwin') {
        console.log(`[CouchDBServiceManager] macOS CouchDB Debug:`);
        console.log(`  - Executable: ${executable}`);
        console.log(`  - Working Dir: ${couchdbPath}`);
        console.log(`  - ROOTDIR: ${spawnEnv.ROOTDIR}`);
        console.log(`  - ERLANG_HOME: ${spawnEnv.ERLANG_HOME}`);
        console.log(`  - COUCHDB_BIN_DIR: ${spawnEnv.COUCHDB_BIN_DIR}`);
        console.log(`  - PATH: ${spawnEnv.PATH?.split(':').slice(0, 3).join(':') + '...'}`);
      }

      // Enhanced macOS config resolution - check multiple locations
      const bundledConfigPath = path.join(couchdbPath, 'etc', 'local.d', '20-embedded.ini');
      const userConfigPath = path.join(this.config.dataDir, 'etc', 'local.ini');
      
      // Use bundled config if available (release mode), otherwise fallback to user config (dev mode)
      const configPath = fs.existsSync(bundledConfigPath) ? bundledConfigPath : userConfigPath;
      
      console.log(`[macOS] Using config file: ${configPath}`);
      spawnArgs = ['-couch_ini', path.join(couchdbPath, 'etc', 'default.ini'), configPath];
      spawnOptions.cwd = couchdbPath;
    }
    
    spawnOptions.env = spawnEnv;
    
    // Enhanced Windows debugging
    if (platform === 'win32') {
      console.log(`[CouchDBServiceManager] Windows CouchDB Debug:`);
      console.log(`  - Executable: ${executable}`);
      console.log(`  - Working Dir: ${couchdbPath}`);
      console.log(`  - Args: ${JSON.stringify(spawnArgs)}`);
      console.log(`  - ROOTDIR: ${spawnEnv.ROOTDIR}`);
      console.log(`  - COUCHDB_BIN_DIR: ${spawnEnv.COUCHDB_BIN_DIR}`);
      console.log(`  - PATH: ${spawnEnv.PATH?.split(';').slice(0, 3).join(';')}...`);
    }

    console.log(`Starting CouchDB with executable: ${executable}`);
    console.log(`Arguments: ${spawnArgs.join(' ')}`);

    this.process = spawn(executable, spawnArgs, spawnOptions);
    console.log(`[CouchDBServiceManager] Debug: Spawned CouchDB process` +
      ` PID=${this.process.pid}, cwd=${couchdbPath}, port=${this.config.port}`);

    this.process.stdout?.on('data', (data) => {
      console.log(`CouchDB stdout: ${data}`);
    });

    this.process.stderr?.on('data', (data) => {
      console.error(`CouchDB stderr: ${data}`);
    });

    this.process.on('exit', (code, signal) => {
      console.log(`CouchDB process exited with code ${code} and signal ${signal}`);
      this.process = null;
      
      if (!this.isShuttingDown && this.restartAttempts < this.maxRestartAttempts) {
        this.restartAttempts++;
        console.log(`Attempting to restart CouchDB (attempt ${this.restartAttempts}/${this.maxRestartAttempts})`);
        setTimeout(() => this.start().catch(console.error), 5000 * this.restartAttempts);
      }
    });

    this.process.on('error', (error) => {
      console.error(`CouchDB process error: ${error}`);
    });
  }

  private async waitForHealth(): Promise<void> {
    if (!this.config) throw new Error('Config not initialized');

    const maxAttempts = 30;
    const delayMs = 1000;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await axios.get(`http://127.0.0.1:${this.config.port}/_up`, {
          timeout: 5000
        });
        
        if (response.status === 200) {
          console.log('CouchDB is healthy');
          return;
        }
      } catch (error) {
        console.log(`Health check attempt ${attempt}/${maxAttempts} failed`);
      }

      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }
    }

    throw new Error('CouchDB failed to become healthy within timeout');
  }

  private async createSystemDatabases(): Promise<void> {
    if (!this.config) throw new Error('Config not initialized');

    const systemDbs = ['_users', '_replicator', '_global_changes'];
    const auth = {
      username: this.config.adminUser,
      password: this.config.adminPassword
    };

    for (const db of systemDbs) {
      try {
        await axios.put(`http://127.0.0.1:${this.config.port}/${db}`, {}, {
          auth,
          timeout: 10000
        });
        console.log(`Created system database: ${db}`);
      } catch (error: any) {
        if (error.response?.status !== 412) {
          console.error(`Failed to create system database ${db}:`, error.message);
        }
      }
    }
  }

  private startHealthMonitoring(): void {
    if (!this.config) return;

    this.healthCheckInterval = setInterval(async () => {
      try {
        await axios.get(`http://127.0.0.1:${this.config!.port}/_up`, {
          timeout: 5000
        });
      } catch (error) {
        console.error('CouchDB health check failed:', error);
        if (this.process && !this.process.killed) {
          console.log('Restarting unhealthy CouchDB process');
          this.process.kill('SIGTERM');
        }
      }
    }, 30000);
  }

  private async cleanup(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    if (this.process && !this.process.killed) {
      this.process.kill('SIGKILL');
      this.process = null;
    }
  }

  private getResourcesPath(): string {
    return app.isPackaged 
      ? path.join(process.resourcesPath)
      : path.join(__dirname, '..', '..', 'resources');
  }
}