import { useState, useEffect, useCallback } from 'react';
import { hybridAutonomousSyncManager, type HybridConfig, type HybridStatus } from '@/lib/services/hybrid-autonomous-sync';

interface UseHybridSyncReturn {
  // Status
  status: HybridStatus;
  isRunning: boolean;
  isConnected: boolean;
  isSyncing: boolean;
  connectionType: 'local' | 'internet' | null;
  
  // Local sync info
  localServers: number;
  localConnected: boolean;
  lastLocalDiscovery: Date | null;
  
  // Internet sync info
  internetPeers: number;
  internetConnected: boolean;
  internetRegistered: boolean;
  lastInternetDiscovery: Date | null;
  
  // Sync statistics
  docsReceived: number;
  docsSent: number;
  lastSync: Date | null;
  
  // Error state
  error: string | null;
  
  // Control functions
  start: () => Promise<void>;
  stop: () => Promise<void>;
  discover: () => Promise<void>;
  updateConfig: (config: Partial<HybridConfig>) => void;
}

interface UseHybridSyncOptions {
  autoStart?: boolean;
  config?: Partial<HybridConfig>;
}

export function useHybridSync(options: UseHybridSyncOptions = {}): UseHybridSyncReturn {
  const [status, setStatus] = useState<HybridStatus>(hybridAutonomousSyncManager.getStatus());
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const unsubscribe = hybridAutonomousSyncManager.onStatusChange((newStatus) => {
      setStatus(newStatus);
      
      if (newStatus.error) {
        setError(newStatus.error);
      } else if (error && newStatus.currentConnection.server) {
        setError(null);
      }
    });

    return unsubscribe;
  }, [error]);

  // Initialize with default config if provided
  useEffect(() => {
    if (options.config) {
      const defaultConfig: HybridConfig = {
        localDiscoveryInterval: 30000,
        localReconnectInterval: 60000,
        internetDiscoveryInterval: 120000, // 2 minutes for internet
        internetReconnectInterval: 180000, // 3 minutes for internet
        maxReconnectAttempts: 5,
        autoStart: options.autoStart ?? false,
        preferLocalSync: true,
        vpsBaseUrl: '',
        authToken: '',
        deviceId: '',
        deviceType: 'mobile',
        ...options.config
      };

      hybridAutonomousSyncManager.initialize(defaultConfig).catch(err => {
        console.error('Failed to initialize hybrid sync:', err);
        setError(err.message);
      });
    }
  }, [options.config, options.autoStart]);

  const start = useCallback(async () => {
    if (!options.config) {
      throw new Error('Hybrid sync config required. Provide config in useHybridSync options.');
    }

    try {
      setError(null);
      await hybridAutonomousSyncManager.start();
    } catch (err: any) {
      console.error('Failed to start hybrid sync:', err);
      setError(err.message || 'Failed to start sync');
      throw err;
    }
  }, [options.config]);

  const stop = useCallback(async () => {
    try {
      await hybridAutonomousSyncManager.stop();
      setError(null);
    } catch (err: any) {
      console.error('Failed to stop hybrid sync:', err);
      setError(err.message || 'Failed to stop sync');
      throw err;
    }
  }, []);

  const discover = useCallback(async () => {
    try {
      setError(null);
      await hybridAutonomousSyncManager.discover();
    } catch (err: any) {
      console.error('Discovery failed:', err);
      setError(err.message || 'Discovery failed');
      throw err;
    }
  }, []);

  const updateConfig = useCallback((config: Partial<HybridConfig>) => {
    hybridAutonomousSyncManager.updateConfig(config);
  }, []);

  return {
    // Status
    status,
    isRunning: status.isRunning,
    isConnected: status.syncStatus.connected,
    isSyncing: status.syncStatus.syncing,
    connectionType: status.currentConnection.type,
    
    // Local sync info
    localServers: status.localServers.length,
    localConnected: status.localConnected,
    lastLocalDiscovery: status.lastLocalDiscovery,
    
    // Internet sync info
    internetPeers: status.internetPeers.length,
    internetConnected: status.internetConnected,
    internetRegistered: status.internetRegistered,
    lastInternetDiscovery: status.lastInternetDiscovery,
    
    // Sync statistics
    docsReceived: status.syncStatus.docsReceived,
    docsSent: status.syncStatus.docsSent,
    lastSync: status.syncStatus.lastSync,
    
    // Error state
    error: error || status.error,
    
    // Control functions
    start,
    stop,
    discover,
    updateConfig
  };
}

// Simplified hook for just local sync (backward compatibility)
export function useLocalSync() {
  return useHybridSync({
    config: {
      localDiscoveryInterval: 30000,
      localReconnectInterval: 60000,
      internetDiscoveryInterval: 999999999, // Effectively disabled
      internetReconnectInterval: 999999999,
      maxReconnectAttempts: 5,
      autoStart: true,
      preferLocalSync: true,
      vpsBaseUrl: '',
      authToken: '',
      deviceId: '',
      deviceType: 'mobile'
    }
  });
}

// Simplified hook for just internet sync
export function useInternetSync(config: {
  vpsBaseUrl: string;
  authToken: string;
  deviceId: string;
  deviceType: 'desktop' | 'mobile';
  deviceRegistration?: {
    ipAddress: string;
    couchdbPort?: number;
  };
}) {
  return useHybridSync({
    config: {
      localDiscoveryInterval: 999999999, // Effectively disabled
      localReconnectInterval: 999999999,
      internetDiscoveryInterval: 60000, // 1 minute for internet-only
      internetReconnectInterval: 120000, // 2 minutes for internet-only
      maxReconnectAttempts: 5,
      autoStart: true,
      preferLocalSync: false,
      ...config
    }
  });
}

// Web browser specific hook (internet-only sync)
export function useWebBrowserSync(config: {
  authToken: string;
  deviceId: string;
  vpsBaseUrl?: string;
}) {
  return useHybridSync({
    config: {
      localDiscoveryInterval: 999999999, // Disabled for web browsers
      localReconnectInterval: 999999999,
      internetDiscoveryInterval: 60000, // 1 minute for web browsers
      internetReconnectInterval: 120000, // 2 minutes for web browsers
      maxReconnectAttempts: 3, // Fewer retries for web browsers
      autoStart: false, // Let user initiate
      preferLocalSync: false, // Internet only
      deviceType: 'mobile', // Web browsers are treated as mobile clients
      vpsBaseUrl: config.vpsBaseUrl || 'https://bistro.icu', // Default to bistro.icu
      ...config
    }
  });
}

export type { UseHybridSyncReturn, UseHybridSyncOptions };