interface DiscoveredServer {
  ip: string;
  port: number;
  version?: string;
  url: string;
  lastSeen?: Date;
  responseTime?: number;
}

interface DiscoveryOptions {
  timeout?: number;
  maxConcurrent?: number;
  smartTermination?: boolean;
  maxServers?: number;
}

interface NetworkEnvironment {
  type: 'home' | 'corporate' | 'mobile' | 'public';
  scanStrategy: 'aggressive' | 'conservative' | 'minimal';
  maxConcurrent: number;
  timeout: number;
}

const DEFAULT_PORTS = [5984, 5985, 5986, 5987];
const COMMON_SERVER_IPS = [1, 10, 100, 110, 200, 254]; // Router/server common IPs
const MAX_SERVERS_TO_FIND = 3; // Stop scanning after finding this many servers

// Server cache for faster subsequent discoveries
interface CachedServer extends DiscoveredServer {
  lastVerified: Date;
  failureCount: number;
}

class ServerCache {
  private cache = new Map<string, CachedServer>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_FAILURES = 3;

  set(server: DiscoveredServer): void {
    const key = `${server.ip}:${server.port}`;
    const existing = this.cache.get(key);
    
    this.cache.set(key, {
      ...server,
      lastSeen: new Date(),
      lastVerified: new Date(),
      failureCount: existing?.failureCount || 0
    });
  }

  get(ip: string, port: number): CachedServer | null {
    const key = `${ip}:${port}`;
    const cached = this.cache.get(key);
    
    if (!cached) return null;
    
    // Check if cache is still valid
    const now = Date.now();
    const age = now - cached.lastVerified.getTime();
    
    if (age > this.CACHE_DURATION || cached.failureCount >= this.MAX_FAILURES) {
      this.cache.delete(key);
      return null;
    }
    
    return cached;
  }

  getAll(): CachedServer[] {
    const now = Date.now();
    const valid: CachedServer[] = [];
    
    for (const [key, server] of this.cache.entries()) {
      const age = now - server.lastVerified.getTime();
      
      if (age <= this.CACHE_DURATION && server.failureCount < this.MAX_FAILURES) {
        valid.push(server);
      } else {
        this.cache.delete(key);
      }
    }
    
    return valid.sort((a, b) => {
      // Sort by response time (faster first), then by failure count
      if (a.responseTime && b.responseTime) {
        return a.responseTime - b.responseTime;
      }
      return a.failureCount - b.failureCount;
    });
  }

  markFailure(ip: string, port: number): void {
    const key = `${ip}:${port}`;
    const cached = this.cache.get(key);
    
    if (cached) {
      cached.failureCount++;
      if (cached.failureCount >= this.MAX_FAILURES) {
        this.cache.delete(key);
      }
    }
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

const serverCache = new ServerCache();

/**
 * Smart subnet detection - automatically detect current network subnet
 * instead of hardcoded ranges for better accuracy and performance
 */
async function detectCurrentSubnet(): Promise<string[]> {
  const subnets: string[] = [];
  
  try {
    // Method 1: Check if we can detect via Electron (most reliable)
    if (typeof window !== 'undefined' && (window as any).electronAPI) {
      try {
        const networkInfo = await (window as any).electronAPI.getNetworkInfo?.();
        if (networkInfo?.subnets) {
          subnets.push(...networkInfo.subnets);
        }
      } catch (e) {
        console.log('Electron network detection not available');
      }
    }
    
    // Method 2: Try to infer from common gateway detection
    const gatewaySubnets = await detectCommonGateways();
    subnets.push(...gatewaySubnets);
    
  } catch (error) {
    console.warn('Auto subnet detection failed, using common defaults:', error);
  }
  
  // Always include common defaults as fallbacks (but prioritize detected ones)
  const fallbacks = ['192.168.1', '192.168.0', '10.0.0', '172.16.0'];
  const uniqueSubnets = [...new Set([...subnets, ...fallbacks])];
  
  console.log(`🔍 Detected subnets for scanning:`, uniqueSubnets);
  return uniqueSubnets;
}

/**
 * Detect common gateway IPs to infer network subnet
 */
async function detectCommonGateways(): Promise<string[]> {
  const commonGateways = [
    '***********', '***********', '***********',
    '********', '********', '**********'
  ];
  
  const detectedSubnets: string[] = [];
  
  // Quick test common gateway IPs to see which subnet we're on
  const gatewayTests = commonGateways.map(async (gateway) => {
    try {
      const isMobile = typeof window !== 'undefined' && 
        (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
         !!(window as any).Capacitor);
      
      if (isMobile) {
        const { CapacitorHttp } = await import('@capacitor/core');
        await CapacitorHttp.get({
          url: `http://${gateway}`,
          headers: { 'Accept': 'text/html' },
          connectTimeout: 1000,
          readTimeout: 1000
        });
      } else {
        const controller = new AbortController();
        setTimeout(() => controller.abort(), 1000);
        await fetch(`http://${gateway}`, { 
          signal: controller.signal,
          mode: 'no-cors' // Avoid CORS issues, we just want to know if it responds
        });
      }
      
      // If we reach here, gateway responded - extract subnet
      const parts = gateway.split('.');
      return `${parts[0]}.${parts[1]}.${parts[2]}`;
    } catch {
      return null;
    }
  });
  
  const results = await Promise.allSettled(gatewayTests);
  results.forEach(result => {
    if (result.status === 'fulfilled' && result.value) {
      detectedSubnets.push(result.value);
    }
  });
  
  return [...new Set(detectedSubnets)];
}

/**
 * Detect network environment to adapt scanning strategy
 */
async function detectNetworkEnvironment(): Promise<NetworkEnvironment> {
  const subnets = await detectCurrentSubnet();
  const primarySubnet = subnets[0];
  
  // Corporate network detection (10.x, 172.x ranges)
  if (primarySubnet?.startsWith('10.') || primarySubnet?.startsWith('172.')) {
    return {
      type: 'corporate',
      scanStrategy: 'conservative',
      maxConcurrent: 5,  // Avoid triggering security alerts
      timeout: 2000
    };
  }
  
  // Home network (192.168.x)
  if (primarySubnet?.startsWith('192.168.')) {
    return {
      type: 'home',
      scanStrategy: 'aggressive',
      maxConcurrent: 25,
      timeout: 1200
    };
  }
  
  // Mobile/public network fallback
  return {
    type: 'mobile',
    scanStrategy: 'minimal',
    maxConcurrent: 3,
    timeout: 3000
  };
}

/**
 * Quick scan of most common server IPs first (much faster than full range)
 */
async function scanCommonIPs(subnet: string, environment: NetworkEnvironment): Promise<DiscoveredServer[]> {
  console.log(`⚡ Quick scan of common IPs in ${subnet}...`);
  
  const { timeout, maxConcurrent } = environment;
  const servers: DiscoveredServer[] = [];
  
  // Test most common server IPs first
  const tests = COMMON_SERVER_IPS.map(ip => 
    testCouchDBServer(`${subnet}.${ip}`, 5984, timeout)
  );
  
  const results = await Promise.allSettled(tests);
  results.forEach(result => {
    if (result.status === 'fulfilled' && result.value) {
      servers.push(result.value);
    }
  });
  
  if (servers.length > 0) {
    console.log(`✅ Found ${servers.length} servers in quick scan`);
  }
  
  return servers;
}

/**
 * Smart full range scan with early termination
 */
async function scanFullRange(subnet: string, environment: NetworkEnvironment, options: DiscoveryOptions = {}): Promise<DiscoveredServer[]> {
  const { maxServers = MAX_SERVERS_TO_FIND, smartTermination = true } = options;
  const { timeout, maxConcurrent } = environment;
  const servers: DiscoveredServer[] = [];
  const BATCH_SIZE = Math.min(15, maxConcurrent); // Smaller batches for faster termination
  
  console.log(`🔄 Full range scan of ${subnet}.x (strategy: ${environment.scanStrategy})`);
  
  for (let i = 1; i <= 254; i += BATCH_SIZE) {
    const batch = [];
    
    for (let j = 0; j < BATCH_SIZE && i + j <= 254; j++) {
      const ip = i + j;
      // Skip common IPs we already checked
      if (!COMMON_SERVER_IPS.includes(ip)) {
        batch.push(testCouchDBServer(`${subnet}.${ip}`, 5984, timeout));
      }
    }
    
    if (batch.length === 0) continue;
    
    const results = await Promise.allSettled(batch);
    const found = results
      .filter(r => r.status === 'fulfilled' && r.value)
      .map(r => r.value as DiscoveredServer);
    
    servers.push(...found);
    
    // SMART TERMINATION: Stop when we have enough servers
    if (smartTermination && servers.length >= maxServers) {
      console.log(`✅ Found ${servers.length} servers, stopping scan early (${Math.round((i/254)*100)}% complete)`);
      break;
    }
  }
  
  return servers;
}

/**
 * Minimal discovery for mobile/restricted networks
 */
async function minimalDiscovery(subnets: string[]): Promise<DiscoveredServer[]> {
  console.log('📱 Minimal discovery mode - checking only common server IPs');
  
  const environment: NetworkEnvironment = {
    type: 'mobile',
    scanStrategy: 'minimal',
    maxConcurrent: 3,
    timeout: 3000
  };
  
  // Only check first 2 subnets and only common IPs
  for (const subnet of subnets.slice(0, 2)) {
    const servers = await scanCommonIPs(subnet, environment);
    if (servers.length > 0) {
      return servers;
    }
  }
  
  return [];
}

async function testCouchDBServer(ip: string, port: number, timeout = 1000): Promise<DiscoveredServer | null> {
  const url = `http://${ip}:${port}`;
  
  // Check cache first for faster response
  const cached = serverCache.get(ip, port);
  if (cached) {
    console.log(`💾 Using cached server: ${url} (${cached.responseTime}ms)`);
    return cached;
  }
  
  const startTime = Date.now();
  
  try {
    let response;
    let data;
    
    // Use CapacitorHttp on mobile, fetch on desktop
    const isMobile = typeof window !== 'undefined' && (window as any).Capacitor;
    
    if (isMobile) {
      console.log(`📱 Mobile request to ${url}`);
      const { CapacitorHttp } = await import('@capacitor/core');
      response = await CapacitorHttp.get({
        url,
        headers: { 'Accept': 'application/json' },
        connectTimeout: timeout,
        readTimeout: timeout
      });
      if (response.status !== 200) {
        serverCache.markFailure(ip, port);
        return null;
      }
      data = response.data;
    } else {
      console.log(`🖥️ Desktop request to ${url}`);
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      response = await fetch(url, {
        method: 'GET',
        signal: controller.signal,
        headers: { 'Accept': 'application/json' }
      });
      
      clearTimeout(timeoutId);
      if (!response.ok) {
        serverCache.markFailure(ip, port);
        return null;
      }
      data = await response.json();
    }
    
    const responseTime = Date.now() - startTime;
    
    if (data && data.couchdb && data.version) {
      console.log(`✅ Found CouchDB: ${url} (${responseTime}ms, ${isMobile ? 'mobile' : 'desktop'})`);
      
      const server: DiscoveredServer = {
        ip,
        port,
        version: data.version,
        url,
        lastSeen: new Date(),
        responseTime
      };
      
      // Cache the successful result
      serverCache.set(server);
      
      return server;
    }
    
    serverCache.markFailure(ip, port);
    return null;
  } catch (error) {
    serverCache.markFailure(ip, port);
    return null;
  }
}


export async function discoverCouchDBServers(options: DiscoveryOptions = {}): Promise<DiscoveredServer[]> {
  console.log('🔍 Starting smart CouchDB server discovery...');
  
  try {
    // First, return any valid cached servers for immediate response
    const cachedServers = serverCache.getAll();
    if (cachedServers.length > 0) {
      console.log(`💾 Found ${cachedServers.length} cached server(s), returning immediately:`, cachedServers.map(s => s.url));
      
      // Start background refresh but return cached results immediately
      setTimeout(async () => {
        try {
          await smartDiscovery(options);
        } catch (error) {
          console.warn('Background discovery refresh failed:', error);
        }
      }, 100);
      
      return cachedServers;
    }
    
    // No cache, perform smart discovery
    return await smartDiscovery(options);
    
  } catch (error) {
    console.error('❌ Smart discovery failed:', error);
    
    // Graceful fallback: return any cached servers we have
    return serverCache.getAll();
  }
}


/**
 * Smart discovery - the new main discovery function
 * Automatically detects network and adapts scanning strategy
 */
async function smartDiscovery(options: DiscoveryOptions = {}): Promise<DiscoveredServer[]> {
  // Step 1: Detect network environment
  const environment = await detectNetworkEnvironment();
  console.log(`🌐 Network environment: ${environment.type} (${environment.scanStrategy})`);
  
  // Step 2: Get actual network subnets
  const subnets = await detectCurrentSubnet();
  
  // Step 3: Apply strategy based on environment
  switch (environment.scanStrategy) {
    case 'minimal':
      return await minimalDiscovery(subnets);
      
    case 'conservative':
      return await conservativeDiscovery(subnets, environment);
      
    case 'aggressive':
      return await aggressiveDiscovery(subnets, environment);
      
    default:
      return await aggressiveDiscovery(subnets, environment);
  }
}

/**
 * Conservative discovery for corporate networks
 */
async function conservativeDiscovery(subnets: string[], environment: NetworkEnvironment): Promise<DiscoveredServer[]> {
  console.log('🏢 Conservative discovery mode - corporate network detected');
  
  // Only scan first subnet with common IPs + limited full scan
  for (const subnet of subnets.slice(0, 1)) {
    // Try common IPs first
    let servers = await scanCommonIPs(subnet, environment);
    if (servers.length > 0) {
      return servers;
    }
    
    // Limited full scan if common IPs fail
    servers = await scanFullRange(subnet, environment, { 
      smartTermination: true, 
      maxServers: 2 
    });
    if (servers.length > 0) {
      return servers;
    }
  }
  
  return [];
}

/**
 * Aggressive discovery for home networks
 */
async function aggressiveDiscovery(subnets: string[], environment: NetworkEnvironment): Promise<DiscoveredServer[]> {
  console.log('🏠 Aggressive discovery mode - home network detected');
  
  for (const subnet of subnets) {
    // Quick scan of common IPs first
    let servers = await scanCommonIPs(subnet, environment);
    if (servers.length > 0) {
      return servers;
    }
    
    // Full range scan with early termination
    servers = await scanFullRange(subnet, environment, { 
      smartTermination: true, 
      maxServers: MAX_SERVERS_TO_FIND 
    });
    if (servers.length > 0) {
      return servers;
    }
  }
  
  return [];
}


// Export cache management functions
export const serverCacheManager = {
  getAll: () => serverCache.getAll(),
  clear: () => serverCache.clear(),
  size: () => serverCache.size(),
  markFailure: (ip: string, port: number) => serverCache.markFailure(ip, port)
};