import { Order, OrderItem } from '@/lib/db/v4/schemas/order-schema';
import { extractDailySequence } from '@/lib/db/v4/operations/order-ops';
import { barcodeService } from '@/lib/services/barcode-service';
import { kitchenQueueService } from '@/lib/services/kitchen-queue-service';
import { getOrderTypeLabel } from '@/lib/types/order-types';
import { printExecutionService, PrintJob as ExecutionPrintJob } from './print-execution-service';
import { shouldShowPrintPreview, isElectronEnvironment, logPrintEnvironmentInfo } from '@/lib/utils/environment';

// 🍽️ Kitchen Printing Systems Service
// Implements the 3 systems from the implementation plan

export interface PrinterConfig {
  id: string;
  name: string;
  ipAddress?: string;
  status: 'online' | 'offline' | 'unknown';
  assignedCategories: string[]; // Categories assigned to this printer (station)
  type: 'thermal' | 'inkjet' | 'laser';
  simulated: boolean;
  isReceiptPrinter?: boolean; // 🆕 Flag to identify receipt printers
  enabled?: boolean; // 🆕 Flag to track if printer is enabled/selected
}

export interface PrintingFeatures {
  queueEnabled: boolean;        // Show queue coordination info on tickets
  barcodeEnabled: boolean;      // Generate barcodes for item completion tracking
}

export interface PrintJob {
  title: string;
  content: string;
  type: 'kitchen' | 'receipt' | 'report' | 'expo';
  printerId?: string;
  stationName?: string;
}

export interface PrintResult {
  success: boolean;
  printJob?: PrintJob;

  printJobs?: PrintJob[];  // For multi-station systems
  showPreview?: boolean;
  error?: string;
  actuallyPrinted?: boolean;
  printExecutionResults?: any[];
  printSuccessRate?: number;
}

export interface ItemStatus {
  orderId: string;
  itemId: string;
  itemName: string;
  status: 'pending' | 'done';
  scannedAt?: string;
  stationId?: string;
  createdAt?: string;
}

interface OSPrinterInput {
  id: string;
  name: string;
  ipAddress?: string;
  status?: 'online' | 'offline' | 'unknown';
  type?: 'thermal' | 'inkjet' | 'laser';
}

import { savePrinterSettings, loadPrinterSettings } from '@/lib/db/v4/operations/printer-settings-ops';

class KitchenPrintService {
  private printers: PrinterConfig[] = [];
  private printingFeatures: PrintingFeatures = { queueEnabled: true, barcodeEnabled: true };
  private itemStatuses: Map<string, ItemStatus> = new Map();
  
  // 🎯 Feature Configuration
  setPrintingFeatures(features: PrintingFeatures) {
    this.printingFeatures = features;
    localStorage.setItem('kitchen_printing_features', JSON.stringify(features));
  }
  
  getPrintingFeatures(): PrintingFeatures {
    const stored = localStorage.getItem('kitchen_printing_features');
    if (stored) {
      try {
        return JSON.parse(stored);
      } catch (error) {
        console.warn('Failed to parse stored printing features, using defaults');
      }
    }
    // Default features
    return { queueEnabled: true, barcodeEnabled: true };
  }
  
  async setPrinters(printers: PrinterConfig[]) {
    this.printers = printers;
    await savePrinterSettings(printers);
  }
  
  async getPrinters(): Promise<PrinterConfig[]> {
    try {
      const printers = await loadPrinterSettings();
      const isElectron = isElectronEnvironment();

      if (printers.length === 0) {
        if (isElectron) {
          console.log('🚀 [PRODUCTION] No printers found or configured. Use the settings page to discover and set up printers.');
        } else {
          console.log('🌐 [PRODUCTION WEB] No printers configured. Kitchen printing requires Electron app.');
        }
        return [];
      }

      // Update internal state
      this.printers = printers;
      
      // Auto-validate and fix category assignments
      try {
        const validation = await this.validateCategoryAssignments();
        if (validation.success && validation.fixed) {
          console.log('🔧 [getPrinters] Auto-fixed invalid printer category assignments');
          // Return the fixed printers
          return this.printers;
        }
      } catch (error) {
        console.warn('⚠️ [getPrinters] Failed to validate category assignments:', error);
      }

      return printers;
    } catch (error) {
      console.error('❌ [getPrinters] Failed to load printer settings:', error);
      return [];
    }
  }
  
  // 🆕 Get receipt printer ID from configuration
  getReceiptPrinterId(): string | null {
    const receiptPrinter = this.printers.find(p => p.isReceiptPrinter && p.status === 'online');
    return receiptPrinter?.id || null;
  }

  /**
   * Get printer name by ID for print execution
   */
  private getPrinterNameById(printerId?: string): string | undefined {
    if (!printerId) return undefined;
    const printer = this.printers.find(p => p.id === printerId);
    return printer?.name;
  }
  
  // 🆕 Set receipt printer by ID
  async setReceiptPrinter(printerId: string | null) {
    this.printers = this.printers.map(printer => ({
      ...printer,
      isReceiptPrinter: printer.id === printerId
    }));
    await this.setPrinters(this.printers);
  }
  
  
  
  // 🎯 Generate simple but reliable barcode ID for kitchen items
  private generateSimpleBarcodeId(orderId: string, itemIndex: number): string {
    // 🎯 COMPACT FORMAT requested by user
    // - Omit year and any non-numeric characters to shorten stripes
    // - Use the *daily sequence* of the order (3 digits) + a 3-digit 1-based
    //   item instance index. Example: dailySequence "002", item #5 ➜ "002005"
    //   This is at most 6 digits, fully numeric → fewer CODE128 bars.

    const dailySeq = extractDailySequence(orderId); // already 3-digit string
    const idxStr = itemIndex.toString().padStart(3, '0');
    return `${dailySeq}${idxStr}`; // e.g. 002005
  }

  // 🎨 Get font sizes based on option
  private getFontSizes(fontSize?: 'small' | 'medium' | 'large') {
    const fontSizeOption = fontSize || 'medium';
    const fontSizes = {
      small: { header: 14, normal: 10, bold: 12 },
      medium: { header: 16, normal: 12, bold: 14 },
      large: { header: 18, normal: 14, bold: 16 }
    };
    return fontSizes[fontSizeOption];
  }
  
  // 🖨️ Unified Multi-Ticket Print Function
  async printKitchenOrder(
    order: Order,
    tableId?: string,
    options: { fontSize?: 'small' | 'medium' | 'large' } = {}
  ): Promise<PrintResult> {
    try {
      this.printingFeatures = this.getPrintingFeatures();
      this.printers = await this.getPrinters();

      console.log('🖨️ [printKitchenOrder] Features:', this.printingFeatures);
      console.log('🖨️ [printKitchenOrder] Available printers:', this.printers.length);

      // 🚨 SAFETY CHECK: No printers configured
      if (!this.printers || this.printers.length === 0) {
        console.warn('[KitchenPrintService] No printers configured. Aborting print.');

        return {
          success: false,
          error: 'No printers configured. Please add a kitchen printer in Settings.'
        };
      }
      
      // 🎯 Add order to queue system if queue feature is enabled
      if (this.printingFeatures.queueEnabled) {
        console.log(`🔄 [printKitchenOrder] Queue enabled - adding order ${order.id} to queue system...`);
        await this.addOrderToQueueSystem(order);
      }
      
      // 🎯 Generate multi-ticket print jobs based on category assignments
      const result = await this.generateMultiTicketPrintJobs(order, tableId, options);

      // 🖨️ EXECUTE ACTUAL PRINTING
      if (result.success && result.printJobs && result.printJobs.length > 0) {
        console.log(`🖨️ [printKitchenOrder] Executing ${result.printJobs.length} print jobs`);

        const printResults: any[] = [];

        for (const printJob of result.printJobs) {
          try {
            // Convert to execution print job format
            const executionJob: ExecutionPrintJob = {
              id: `kitchen-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
              title: printJob.title,
              content: printJob.content,
              type: printJob.type as 'kitchen',
              printerName: this.getPrinterNameById(printJob.printerId),
              copies: 1,
              priority: 'high',
              createdAt: new Date().toISOString()
            };

            // Execute print immediately for kitchen orders (high priority)
            const printResult = await printExecutionService.executePrint(executionJob);
            printResults.push(printResult);

            console.log(`🖨️ [printKitchenOrder] Print job ${executionJob.id} result:`, {
              success: printResult.success,
              printer: printResult.printerUsed,
              error: printResult.error
            });

          } catch (error) {
            console.error(`🖨️ [printKitchenOrder] Failed to execute print job:`, error);
            printResults.push({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown print error'
            });
          }
        }

        // Update result with actual print execution status
        const successfulPrints = printResults.filter(r => r.success).length;
        const totalPrints = printResults.length;

        result.actuallyPrinted = successfulPrints > 0;
        result.printExecutionResults = printResults;
        result.printSuccessRate = totalPrints > 0 ? (successfulPrints / totalPrints) * 100 : 0;

        console.log(`🖨️ [printKitchenOrder] Print execution summary: ${successfulPrints}/${totalPrints} successful`);
      }

      // Use centralized environment utilities for preview logic
      const isElectron = isElectronEnvironment();
      const hasActualJobs = result.printJobs ? result.printJobs.length > 0 : false;
      result.showPreview = shouldShowPrintPreview(result.actuallyPrinted || false, hasActualJobs);

      // Log environment info in development
      logPrintEnvironmentInfo();

      return result;
    } catch (error) {
      console.error('Kitchen print error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown print error'
      };
    }
  }
  
  // 🎯 Unified Multi-Ticket Print Job Generator
  private async generateMultiTicketPrintJobs(
    order: Order,
    tableId?: string,
    options: { fontSize?: 'small' | 'medium' | 'large' } = {}
  ): Promise<PrintResult> {
    const stationItems = await this.splitOrderByStation(order);
    const printJobs: PrintJob[] = [];
    
    // If only one printer or all items go to one printer, can still generate multiple tickets
    for (const [stationId, items] of Object.entries(stationItems)) {
      const printer = this.printers.find(p => p.id === stationId);
      if (!printer || !printer.enabled || items.length === 0) continue;
      
      let content: string;
      
      if (this.printingFeatures.barcodeEnabled) {
        // Generate barcode tickets with per-item barcodes
        this.buildBarcodeItemStatuses(order);
        content = await this.generateBarcodeContent(
          order, 
          items[0].categoryId || '', 
          this.getFontSizes(options.fontSize),
          tableId
        );
      } else if (this.printingFeatures.queueEnabled) {
        // Generate multi-station style tickets with queue coordination
        content = await this.generateMultiStationTicket(
          order, 
          items, 
          printer, 
          stationItems, 
          tableId, 
          options
        );
      } else {
        // Generate simple tickets without queue info or barcodes
        content = await this.generateSimpleTicket(
          order,
          items,
          printer,
          tableId,
          options
        );
      }
      
      const dailySequence = extractDailySequence(order.id);
      printJobs.push({
        title: `${printer.name} - Order #${dailySequence}`,
        content,
        type: 'kitchen',
        printerId: printer.id,
        stationName: printer.name
      });
    }
    
    // Handle case where no station assignments exist - print to first available printer
    if (printJobs.length === 0 && this.printers.length > 0) {
      // 🔧 FIX: Use enabled printers instead of online status
      const enabledPrinters = this.printers.filter(p => p.enabled);

      if (enabledPrinters.length > 0) {
        // Try to find a kitchen printer first, then any enabled printer
        const fallbackPrinter = enabledPrinters.find(p =>
          p.name.toLowerCase().includes('kitchen') ||
          p.name.toLowerCase().includes('main') ||
          p.isReceiptPrinter === false
        ) || enabledPrinters[0];

        console.log('🔧 [generateMultiTicketPrintJobs] Using fallback printer:', fallbackPrinter.name, 'for order with no station assignments');

        const content = await this.generateSingleSystemTicket(order, tableId, options);

        const dailySequence = extractDailySequence(order.id);
        printJobs.push({
          title: `Kitchen Order #${dailySequence}`,
          content,
          type: 'kitchen',
          printerId: fallbackPrinter.id
        });
      } else {
        console.error('❌ [generateMultiTicketPrintJobs] No enabled printers available for fallback');
      }
    }
    
    // 🔍 FINAL DIAGNOSTIC: Log the result
    console.log('🔍 [generateMultiTicketPrintJobs] Generated print jobs:', printJobs.length);
    printJobs.forEach((job, index) => {
      console.log(`🔍 Job ${index + 1}: ${job.title} -> Printer: ${job.printerId}`);
    });

    return {
      success: printJobs.length > 0,
      printJobs: printJobs,
      printJob: printJobs[0], // Keep for backward compatibility
      showPreview: true,
      error: printJobs.length === 0 ? 'No print jobs could be generated. Check printer assignments.' : undefined
    };
  }

  // 🎯 NEW: Add order to queue system for proper tracking
  private async addOrderToQueueSystem(order: Order): Promise<void> {
    try {
      const stationItems = await this.splitOrderByStation(order);
      
      // Add order to each station's queue
      for (const [stationId, items] of Object.entries(stationItems)) {
        if (items.length > 0) {
          console.log(`📋 [addOrderToQueueSystem] Adding order ${order.id} to station ${stationId} with ${items.length} items`);
          await kitchenQueueService.addOrderToQueue(order, stationId, items);
        }
      }
      
      console.log(`✅ [addOrderToQueueSystem] Order ${order.id} successfully added to all relevant station queues`);
    } catch (error) {
      console.error(`❌ [addOrderToQueueSystem] Error adding order ${order.id} to queue:`, error);
      // Don't throw error here - printing should continue even if queue fails
    }
  }
  
  
  // 🏷️ Queue Context Functions
  async getStationQueueContext(stationId: string): Promise<{ totalPendingItems: number }> {
    try {
      // Use queue operations directly for more accurate queue information
      const { getStationQueue } = await import('@/lib/db/v4/operations/queue-ops');
      const stationQueue = await getStationQueue(stationId);
      
      // Calculate total individual items in pending and in-progress orders
      const totalPendingItems = stationQueue.items
        .filter(item => item.status === 'pending' || item.status === 'in-progress')
        .reduce((sum, queueItem) => {
          return sum + queueItem.items.reduce((itemSum, orderItem) => itemSum + orderItem.quantity, 0);
        }, 0);

      console.log(`📊 [getStationQueueContext] Station ${stationId} queue:`, {
        pendingOrders: stationQueue.pendingOrders,
        inProgressOrders: stationQueue.inProgressOrders,
        totalOrders: stationQueue.totalOrders,
        totalPendingItems: totalPendingItems,
        queueItems: stationQueue.items.map(item => ({
          orderId: item.orderId,
          status: item.status,
          itemCount: item.items.reduce((sum, orderItem) => sum + orderItem.quantity, 0),
          createdAt: item.createdAt
        }))
      });
      
      return {
        totalPendingItems: totalPendingItems
      };
    } catch (error) {
      console.error('❌ Error getting station queue context:', error);
      return { totalPendingItems: 0 };
    }
  }

  // 📊 Get other categories info for kitchen coordination
  private async getOtherCategoriesInfo(
    allStationItems: Record<string, OrderItem[]>, 
    currentStationId: string
  ): Promise<Array<{ name: string; items: OrderItem[]; queueCount: number }>> {
    const otherCategories: Array<{ name: string; items: OrderItem[]; queueCount: number }> = [];
    
    console.log(`🔍 [getOtherCategoriesInfo] Checking other stations for current station: ${currentStationId}`);
    console.log(`🔍 [getOtherCategoriesInfo] Available printers:`, this.printers.map(p => ({ id: p.id, name: p.name, categories: p.assignedCategories })));
    console.log(`🔍 [getOtherCategoriesInfo] All station items:`, Object.keys(allStationItems).map(stationId => ({ stationId, itemCount: allStationItems[stationId]?.length || 0 })));
    
    // Get menu to resolve category names
    let menu;
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      menu = await getMenu();
    } catch (error) {
      console.warn('⚠️ Could not load menu for category names:', error);
    }
    
    // Check each printer/station (excluding current station)
    for (const printer of this.printers) {
      const stationId = printer.id;
      
      // Skip current station
      if (stationId === currentStationId) {
        console.log(`⏭️ [getOtherCategoriesInfo] Skipping current station: ${stationId}`);
        continue;
      }
      
      // Check if station has items from current order
      const hasCurrentOrderItems = allStationItems[stationId] && allStationItems[stationId].length > 0;
      
      // Get queue context for this station
      const queueContext = await this.getStationQueueContext(stationId);
      const hasQueueItems = queueContext.totalPendingItems > 0;
      
      console.log(`🔍 [getOtherCategoriesInfo] Station ${stationId} (${printer.name}):`, {
        hasCurrentOrderItems,
        currentOrderItemCount: allStationItems[stationId]?.length || 0,
        hasQueueItems,
        queueCount: queueContext.totalPendingItems
      });
      
      // Include station if it has current order items OR queue items
      if (hasCurrentOrderItems || hasQueueItems) {
        let categoryName = printer.name || stationId;
        
        // Clean up printer name (remove "Printer" suffix)
        categoryName = categoryName.replace(/ Printer$/, '');
        
        // Get real category name from menu if available
        if (menu && printer.assignedCategories?.[0]) {
          const category = menu.categories.find(c => c.id === printer.assignedCategories[0]);
          if (category) {
            categoryName = category.name;
          }
        }
        
        // Use current order items if available, otherwise empty array
        const items = allStationItems[stationId] || [];
        
        console.log(`✅ [getOtherCategoriesInfo] Adding station ${stationId} (${categoryName}) with ${items.length} current items and ${queueContext.totalPendingItems} queue items`);
        
        otherCategories.push({
          name: categoryName,
          items: items,
          queueCount: queueContext.totalPendingItems
        });
      } else {
        console.log(`⏭️ [getOtherCategoriesInfo] Skipping station ${stationId} - no current items or queue`);
      }
    }
    
    // Sort by priority: stations with current order items first, then by queue count
    otherCategories.sort((a, b) => {
      if (a.items.length > 0 && b.items.length === 0) return -1;
      if (a.items.length === 0 && b.items.length > 0) return 1;
      return b.queueCount - a.queueCount; // Higher queue count first
    });
    
    console.log(`🔍 [getOtherCategoriesInfo] Final other categories (sorted):`, otherCategories.map(c => ({ name: c.name, itemCount: c.items.length, queueCount: c.queueCount })));
    
    return otherCategories;
  }

  private async getPendingOrdersForStation(stationId: string): Promise<string[]> {
    try {
      // Import here to avoid circular dependency
      const { getPendingOrders } = await import('@/lib/db/v4/operations/order-completion-ops');
      const pendingOrders = await getPendingOrders();
      
      // Filter orders that have items for this station and are not served
      const stationOrders = pendingOrders.filter(order => {
        return order.stationItems[stationId] && 
               !Object.values(order.stationItems[stationId]).every(item => item.completed);
      });
      
      return stationOrders.map(order => order.orderId);
    } catch (error) {
      console.error('❌ Error getting pending orders for station:', error);
      return [];
    }
  }

  
  
  // 🔧 Helper method to get cached menu categories for fallback resolution
  private getCachedMenuCategories(): any[] | null {
    try {
      // Try to get menu from a global cache or import it synchronously if possible
      // This is a fallback mechanism and should not be the primary way to get category names
      if (typeof window !== 'undefined' && (window as any).__menuCache) {
        return (window as any).__menuCache.categories || null;
      }
      return null;
    } catch (error) {
      console.warn('⚠️ [getCachedMenuCategories] Failed to get cached menu categories:', error);
      return null;
    }
  }

  // 📊 Split Order by Station (Using REAL Categories) - IMPROVED
  private async splitOrderByStation(order: Order): Promise<Record<string, OrderItem[]>> {
    const stationItems: Record<string, OrderItem[]> = {};

    // Initialize enabled stations only
    const enabledPrinters = this.printers.filter(printer => printer.enabled);
    if (enabledPrinters.length === 0) {
      throw new Error('🖨️ No enabled printers configured. Configure printers in settings before printing.');
    }
    enabledPrinters.forEach(printer => {
      stationItems[printer.id] = [];
    });

    // 🔍 DIAGNOSTIC: Log printer and order information
    console.log('🔍 [splitOrderByStation] DIAGNOSTIC INFO:');
    console.log('🔍 Enabled printers:', enabledPrinters.map(p => ({
      id: p.id,
      name: p.name,
      assignedCategories: p.assignedCategories
    })));
    console.log('🔍 Order items:', order.items.map(item => ({
      name: item.name,
      menuItemId: item.menuItemId,
      categoryName: (item as any).categoryName,
      categoryId: item.categoryId
    })));

    // Load menu data once for fallback resolution
    let menuCategories: any[] | null = null;
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      menuCategories = menu.categories || [];
      console.log('🔍 [splitOrderByStation] Loaded menu categories for fallback resolution:', menuCategories.length);
    } catch (error) {
      console.warn('⚠️ [splitOrderByStation] Failed to load menu for category resolution:', error);
    }

    order.items.forEach(item => {
      console.log('🔍 [splitOrderByStation] Processing item:', item.name, 'categoryName:', (item as any).categoryName, 'categoryId:', item.categoryId, 'size:', item.size);

      // Try to get categoryName, with multiple fallback mechanisms
      let categoryName = (item as any).categoryName;

      // Fallback 1: Try to resolve categoryName from categoryId using menu data
      if (!categoryName && item.categoryId && menuCategories) {
        console.log('🔄 [splitOrderByStation] No categoryName found, attempting to resolve from categoryId:', item.categoryId);
        const category = menuCategories.find(cat => cat.id === item.categoryId);
        if (category) {
          categoryName = category.name;
          console.log('✅ [splitOrderByStation] Resolved categoryName from categoryId:', categoryName);
        }
      }


      if (!categoryName) {
        throw new Error(`🖨️ Item "${item.name}" has no categoryName and could not resolve from categoryId "${item.categoryId}". All items must have a valid categoryName for printing.`);
      }

      const assignedPrinter = enabledPrinters.find(printer =>
        printer.assignedCategories.includes(categoryName)
      );

      if (!assignedPrinter) {
        console.error('❌ [splitOrderByStation] No printer found for item:', item.name, '| size:', item.size);
        console.error('❌ Item categoryName:', categoryName, '| categoryId:', item.categoryId);
        console.error('❌ Available printers:', enabledPrinters.map(p => ({
          name: p.name,
          id: p.id,
          assignedCategories: p.assignedCategories
        })));
        console.error('❌ All order items:', order.items.map(i => ({ name: i.name, categoryName: (i as any).categoryName, size: i.size })));
        throw new Error(`🖨️ No printer assigned to category "${categoryName}" for item "${item.name}" (size: ${item.size}). Configure printer assignments in settings.`);
      }

      console.log('✅ [splitOrderByStation] Found printer by categoryName:', assignedPrinter.name, 'for item:', item.name);
      console.log('📋 [splitOrderByStation] Assigning item', item.name, 'to printer:', assignedPrinter.name);

      stationItems[assignedPrinter.id].push(item);
    });
    
    // Log the distribution for debugging
    console.log('📊 Station distribution:', Object.entries(stationItems).map(([id, items]) => ({
      station: this.printers.find(p => p.id === id)?.name,
      items: items.length,
      itemNames: items.map(i => i.name)
    })));
    
    return stationItems;
  }
  
  
  // 🎯 SINGLE SOURCE OF TRUTH - Master Print Content Generator
  private async generatePrintContent(order: Order, options: {
    type: 'single' | 'multi-station' | 'barcode' | 'expo' | 'receipt';
    stationId?: string;
    tableId?: string;
    fontSize?: 'small' | 'medium' | 'large';
    printer?: PrinterConfig;
    stationItems?: OrderItem[];
    allStationItems?: Record<string, OrderItem[]>;
    currentStationIndex?: number;
    totalStations?: number;
    payment?: { method: string; received: number; change: number };
  }): Promise<string> {
    const fontSize = options.fontSize || 'medium';
    const fontSizes = {
      small: { header: 14, normal: 10, bold: 12 },
      medium: { header: 16, normal: 12, bold: 14 },
      large: { header: 18, normal: 14, bold: 16 }
    };
    const fs = fontSizes[fontSize];

    switch (options.type) {
      case 'single':
        return await this.generateSingleSystemContent(order, fs, options.tableId);
      case 'multi-station':
        return await this.generateMultiStationContent(order, options.stationId!, fs, options.tableId);
      case 'barcode':
        return await this.generateBarcodeContent(order, options.stationId!, fs, options.tableId);
      case 'expo':
        return this.generateExpoContent(order, fs, options.tableId);
      case 'receipt':
        return await this.generateReceiptContent(order, fs, options.tableId, options.payment);
      default:
        throw new Error(`Unknown print type: ${options.type}`);
    }
  }

  /**
   * Helper to generate the common HTML wrapper for all print jobs.
   * This includes the base styling, header (title, order ID, table ID, timestamp),
   * and a slot for the main content and optional order notes.
   */
  private async generatePrintHtmlWrapper(
    jobType: 'kitchen' | 'receipt' | 'expo', // New parameter to differentiate print types
    order: Order,
    fs: { header: number; bold: number; normal: number },
    mainContentHtml: string,
    options?: { // Use an options object for clarity
      tableId?: string;
      printDate?: Date;
      categoryOrStationName?: string; // Specific for kitchen tickets
      receiptPaymentInfo?: { method: string; received: number; change: number }; // Specific for receipts
    }
  ): Promise<string> {
    // 🎯 FIX: Use order creation time, not current time
    const orderCreationDate = new Date(order.createdAt);
    const dailySequence = extractDailySequence(order.id);

    let headerHtml = '';

    if (jobType === 'kitchen') {
      // 🎯 NEW COMPACT HEADER FORMAT BASED ON USER REQUIREMENTS
      const timeString = orderCreationDate.toLocaleTimeString('en-GB', { 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit' 
      }); // HH:MM:SS format
      
      const dateString = orderCreationDate.toLocaleDateString('en-GB', { 
        day: '2-digit', 
        month: '2-digit' 
      }); // DD/MM format
      
      const categoryOrStationName = options?.categoryOrStationName || 'KITCHEN';
      
      headerHtml = `
      <div style="text-align: center; margin-bottom: 2px;">
        <div style="font-size: ${fs.bold}px; font-weight: bold; line-height: 1;">${timeString} #${dailySequence}</div>
        <div style="font-size: ${fs.normal - 2}px; margin-top: 1px;">${dateString}</div>
      </div>
      `;
      
      // 🎯 FIX: Resolve table ID to table name and add order type in French
      if (options?.tableId || order.orderType) {
        let tableInfo = '';
        
        // Resolve table name if we have a tableId
        if (options?.tableId) {
          try {
            const { getTable } = await import('@/lib/db/v4/operations/table-ops');
            const table = await getTable(options.tableId);
            tableInfo = table?.name ? `TABLE ${table.name}` : `TABLE ${options.tableId}`;
          } catch (error) {
            console.warn('⚠️ Could not resolve table name:', error);
            tableInfo = `TABLE ${options.tableId}`;
          }
        }
        
        // 🎯 STANDARDIZED: Use centralized order type labels
        const orderTypeText = order.orderType ? getOrderTypeLabel(order.orderType) : '';
        
        // Combine table info and order type
        const infoLine = [tableInfo, orderTypeText].filter(Boolean).join(' • ');
        
        if (infoLine) {
          headerHtml += `<div style="font-size: ${fs.normal - 1}px; text-align: center; margin-bottom: 2px;">${infoLine}</div>`;
        }
      }
    } else if (jobType === 'receipt') {
      // For receipts, don't add any header here - it's handled by generateReceiptContent
      headerHtml = '';
    } else if (jobType === 'expo') {
      // 🎯 FIX: Also resolve table name for expo tickets
      let tableInfo = '';
      if (options?.tableId) {
        try {
          const { getTable } = await import('@/lib/db/v4/operations/table-ops');
          const table = await getTable(options.tableId);
          tableInfo = table?.name ? `Table ${table.name}` : `Table ${options.tableId}`;
        } catch (error) {
          console.warn('⚠️ Could not resolve table name for expo:', error);
          tableInfo = `Table ${options.tableId}`;
        }
      }
      
      headerHtml = `
      <div style="text-align: center; margin-bottom: 3px;">
        <div style="font-size: ${fs.bold}px; font-weight: bold;">EXPO TICKET</div>
        <div style="font-size: ${fs.normal}px;">ORDER #${dailySequence}</div>
        ${tableInfo ? `<div style="font-size: ${fs.normal - 1}px;">${tableInfo}</div>` : ''}
        <div style="font-size: ${fs.normal - 2}px;">${orderCreationDate.toLocaleString()}</div>
      </div>
      `;
    } else {
      // Fallback for any other unexpected jobType or generic title-based print (should not happen with defined types)
      headerHtml = `
      <div style="text-align: center; margin-bottom: 3px;">
        <div style="font-size: ${fs.bold}px; font-weight: bold;">${options?.categoryOrStationName || 'ORDER'}</div>
        <div style="font-size: ${fs.normal}px;">ORDER #${dailySequence}</div>
        ${options?.tableId ? `<div style="font-size: ${fs.normal - 1}px;">TABLE ${options.tableId}</div>` : ''}
        <div style="font-size: ${fs.normal - 2}px;">${orderCreationDate.toLocaleString()}</div>
      </div>
      `;
    }

    let html = `
<div style="font-family: 'Courier New', monospace; width: 58mm; max-width: 220px; margin: 0; padding: 1px; line-height: 1.0;">
  ${headerHtml}
  ${mainContentHtml}
`;

    // Only show order notes for kitchen tickets, not receipts
    if (order.notes && jobType !== 'receipt') {
      html += `
  <div style="margin-top: 3px; padding: 2px; border: 1px solid #000;">
    <div style="font-size: ${fs.normal - 1}px; font-weight: bold;">ORDER NOTES:</div>
    <div style="font-size: ${fs.normal - 1}px;">${order.notes}</div>
  </div>`;
    }

    html += `
</div>`;

    return html;
  }

  // 🍕 Render Kitchen Item (handles both regular items and custom pizzas) - ULTRA COMPACT FORMAT
  private renderKitchenItem(item: OrderItem, fs: { header: number; bold: number; normal: number }): string {
    let itemContent = '';
    
    // 🍕 Handle Custom Pizza Display - ULTRA COMPACT WITH VISUAL SEPARATION
    if (item.compositeType === 'pizza_quarters' && item.quarters && item.quarters.length > 0) {
      // Main item line: Pizza Personnalisée (Large) - no multiplier
      itemContent += `<div style="font-size: ${fs.normal}px; font-weight: bold; margin-bottom: 1px;">${item.name}${item.size ? ` (${item.size})` : ''}</div>`;
      
      // Show sections distinctively (determine if 2 or 4 sections)
      const totalQuarters = item.quarters.length;
      const sectionType = totalQuarters === 2 ? '2 sections' : '4 sections';
      itemContent += `<div style="font-size: ${fs.normal - 1}px; font-style: italic; margin-bottom: 2px; color: #666;">[${sectionType}]</div>`;
      
      // List each quarter individually without grouping
      item.quarters.forEach((quarter, index) => {
        const sectionNumber = index + 1;
        itemContent += `<div style="font-size: ${fs.normal - 2}px; margin-bottom: 1px; padding-left: 2px; border-left: 1px solid #000;">▸ Section ${sectionNumber}: ${quarter.name}</div>`;
      });
    } else {
      // Regular item display - ONE LINE FORMAT: itemname (size) - no multiplier
      itemContent += `<div style="font-size: ${fs.normal}px; font-weight: bold; margin-bottom: 0px;">${item.name}${item.size ? ` (${item.size})` : ''}</div>`;
    }

    // Show addons in compact format
    if (item.addons?.length) {
      itemContent += `<div style="font-size: ${fs.normal - 2}px; margin-bottom: 0px; line-height: 1.0;">+${item.addons.map(a => a.name).join(', ')}</div>`;
    }

    // Show notes in compact format
    if (item.notes) {
      itemContent += `<div style="font-size: ${fs.normal - 2}px; font-weight: bold; margin-bottom: 0px; line-height: 1.0;">⚠️ ${item.notes}</div>`;
    }
    
    return itemContent;
  }

  // 🍳 Generate Single System Content - ULTRA COMPACT
  private async generateSingleSystemContent(
    order: Order,
    fs: { header: number; bold: number; normal: number },
    tableId?: string
  ): Promise<string> {
    let mainContent = '';

    // Group items by category for better organization
    const itemsByCategory: Record<string, OrderItem[]> = {};
    order.items.forEach(item => {
      const category = item.categoryId || 'Uncategorized';
      if (!itemsByCategory[category]) {
        itemsByCategory[category] = [];
      }
      itemsByCategory[category].push(item);
    });

    // Get menu to resolve category names
    let menu;
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      menu = await getMenu();
    } catch (error) {
      console.warn('⚠️ Could not load menu for category names:', error);
    }

    // 🎯 NEW COMPACT FORMAT: Category directly followed by items
    Object.entries(itemsByCategory).forEach(([categoryId, items], index) => {
      // 🎯 FIX: Get actual category name instead of ID
      let categoryName = categoryId.toUpperCase();
      if (menu && categoryId !== 'Uncategorized') {
        const category = menu.categories.find(c => c.id === categoryId);
        if (category) {
          categoryName = category.name.toUpperCase();
        }
      }
      
      // Category header - ultra compact, no wasted space
      mainContent += `<div style="font-size: ${fs.normal}px; font-weight: bold; margin-top: ${index > 0 ? '1px' : '0'}; margin-bottom: 0px; line-height: 1.0;">${categoryName}:</div>`;

      // Items in ultra compact format
      items.forEach(item => {
        mainContent += `<div style="margin-bottom: 0px; margin-left: 1px;">${this.renderKitchenItem(item, fs)}</div>`;
      });
    });

    // Pass 'kitchen' jobType and the generic 'KITCHEN' as categoryOrStationName for single system
    return await this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: 'KITCHEN' });
  }

  // 🏪 Generate Multi-Station Content - ULTRA COMPACT
  private async generateMultiStationContent(
    order: Order,
    categoryId: string,
    fs: { header: number; bold: number; normal: number },
    tableId?: string
  ): Promise<string> {
    const categoryItems = order.items.filter(item => item.categoryId === categoryId);
    
    // 🎯 FIX: Get actual category name instead of ID
    let categoryName = categoryId.toUpperCase();
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      if (category) {
        categoryName = category.name.toUpperCase();
      }
    } catch (error) {
      console.warn('⚠️ Could not load menu for category name:', error);
    }
    
    let mainContent = '';
    
    categoryItems.forEach(item => {
      mainContent += `<div style="margin-bottom: 1px;">${this.renderKitchenItem(item, fs)}</div>`;
    });

    // Queue context for other stations - ultra compact display
    const otherCategoriesInfo = await this.getOtherCategoriesInfo(
      { [categoryId]: categoryItems }, 
      categoryId
    );
    
    if (otherCategoriesInfo.length > 0) {
      const otherStationsText = otherCategoriesInfo
        .map(info => `${info.name}: ${info.queueCount}`)
        .join(' • ');
      mainContent += `
      <div style="margin-top: 3px; padding: 1px; border-top: 1px solid #000; font-size: ${fs.normal - 2}px; text-align: center;">
        ${otherStationsText}
      </div>`;
    }

    return await this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: categoryName });
  }

  // 🎯 Generate Simple Ticket Content (no queue, no barcodes)
  private async generateSimpleTicket(
    order: Order,
    stationItems: OrderItem[],
    printer: PrinterConfig,
    tableId?: string,
    options: { fontSize?: 'small' | 'medium' | 'large' } = {}
  ): Promise<string> {
    const fs = this.getFontSizes(options.fontSize);
    const categoryId = printer.assignedCategories[0] || printer.name.replace(/ Printer$/, '');
    
    // Get actual category name instead of ID
    let categoryName = categoryId.toUpperCase();
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      if (category) {
        categoryName = category.name.toUpperCase();
      }
    } catch (error) {
      console.warn('⚠️ Could not load menu for category name:', error);
    }
    
    let mainContent = `<div style="font-size: ${fs.bold}px; font-weight: bold; margin-bottom: 2px;">${categoryName}:</div>`;
    
    // Simple item listing - no queue info or barcodes
    stationItems.forEach(item => {
      mainContent += `<div style="margin-bottom: 2px; margin-left: 4px;">${this.renderKitchenItem(item, fs)}</div>`;
    });
    
    return this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: categoryName });
  }

  // 🎯 Calculate global item index across entire order (not per-category)
  private calculateGlobalItemIndex(order: Order, targetCategoryId: string, targetItemIndex: number): number {
    let globalIndex = 1;
    
    // Iterate through all items in order to find the global position
    for (const item of order.items) {
      for (let i = 0; i < item.quantity; i++) {
        if (item.categoryId === targetCategoryId && globalIndex === targetItemIndex) {
          return globalIndex;
        }
        globalIndex++;
      }
    }
    
    return globalIndex;
  }

  // 🎯 Generate Barcode Content - ULTRA COMPACT with per-item barcodes
  private async generateBarcodeContent(
    order: Order,
    categoryId: string,
    fs: { header: number; bold: number; normal: number },
    tableId?: string
  ): Promise<string> {
    const categoryItems = order.items.filter(item => item.categoryId === categoryId);
    
    // 🎯 FIX: Get actual category name instead of ID
    let categoryName = categoryId.toUpperCase();
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      if (category) {
        categoryName = category.name.toUpperCase();
      }
    } catch (error) {
      console.warn('⚠️ Could not load menu for category name:', error);
    }
    
    let mainContent = '';
    let globalIndex = this.calculateGlobalItemIndex(order, categoryId, 0);
    
    // 🔧 Build barcode statuses for this order if not already done
    this.buildBarcodeItemStatuses(order);
    
    // Import barcode service for barcode generation
    const { barcodeService } = await import('@/lib/services/barcode-service');
    
    categoryItems.forEach(item => {
      for (let i = 0; i < item.quantity; i++) {
        const barcodeId = this.generateSimpleBarcodeId(order.id, globalIndex);
        const barcodeDataURL = barcodeService.generateKitchenBarcodeDataURL(barcodeId);
        
        // Create ultra-compact item block with barcode
        mainContent += `
        <div style="margin-bottom: 1px; padding: 1px; border: 1px solid #000;">
          ${this.renderKitchenItem({ ...item, quantity: 1 }, fs)}
          <div style="text-align: center; margin-top: 1px;">
            <img src="${barcodeDataURL}" style="max-width: 170px; height: 20px; display: block; margin: 0 auto;" />
            <div style="font-size: ${fs.normal - 4}px; margin-top: 0px; line-height: 1;">${barcodeId}</div>
          </div>
        </div>`;
        globalIndex++;
      }
    });

    // Queue context for other stations - ultra compact display
    const otherCategoriesInfo = await this.getOtherCategoriesInfo(
      { [categoryId]: categoryItems }, 
      categoryId
    );
    
    if (otherCategoriesInfo.length > 0) {
      const otherStationsText = otherCategoriesInfo
        .map(info => `${info.name}: ${info.queueCount}`)
        .join(' • ');
      mainContent += `
      <div style="margin-top: 3px; padding: 1px; border-top: 1px solid #000; font-size: ${fs.normal - 2}px; text-align: center;">
        ${otherStationsText}
      </div>`;
    }

    return await this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: categoryName });
  }

  // 🎯 Generate Expo Content - used for expo tickets that show overview of full order
  private async generateExpoContent(
    order: Order,
    fs: { header: number; bold: number; normal: number },
    tableId?: string
  ): Promise<string> {
    let mainContent = '';

    // 🔧 Render items in a compact format
    const renderItems = (itemsToRender: OrderItem[], fs: { header: number; bold: number; normal: number }) => {
      const renderedItems: string[] = [];
      itemsToRender.forEach(item => {
        // Render each item individually based on quantity
        for (let i = 0; i < item.quantity; i++) {
          renderedItems.push(`<div style="margin-bottom: 1px; font-size: ${fs.normal}px;">• ${item.name}${item.size ? ` (${item.size})` : ''}</div>`);
        }
      });
      return renderedItems.join('');
    };

    // Group items by category for better organization
    const itemsByCategory: Record<string, OrderItem[]> = {};
    order.items.forEach(item => {
      const category = item.categoryId || 'Uncategorized';
      if (!itemsByCategory[category]) {
        itemsByCategory[category] = [];
      }
      itemsByCategory[category].push(item);
    });

    // Get menu to resolve category names
    let menu;
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      menu = await getMenu();
    } catch (error) {
      console.warn('⚠️ Could not load menu for category names:', error);
    }

    Object.entries(itemsByCategory).forEach(([categoryId, items], index) => {
      // 🎯 FIX: Get actual category name instead of ID
      let categoryName = categoryId.toUpperCase();
      if (menu && categoryId !== 'Uncategorized') {
        const category = menu.categories.find(c => c.id === categoryId);
        if (category) {
          categoryName = category.name.toUpperCase();
        }
      }
      
      mainContent += `<div style="font-size: ${fs.normal}px; font-weight: bold; margin-top: ${index > 0 ? '2px' : '0'}; margin-bottom: 1px;">${categoryName}:</div>`;
      mainContent += `<div style="margin-left: 4px;">${renderItems(items, fs)}</div>`;
    });

    mainContent += `
    <div style="margin-top: 4px; padding: 2px; border: 1px solid #000; text-align: center;">
      <div style="font-size: ${fs.normal}px; font-weight: bold;">READY FOR PICKUP</div>
    </div>`;

    return await this.generatePrintHtmlWrapper('expo', order, fs, mainContent, { tableId });
  }

  // 🧾 Generate Receipt Content
  private async generateReceiptContent(
    order: Order, 
    fs: { header: number; bold: number; normal: number },
    tableId?: string,
    payment?: { method: string; received: number; change: number }
  ): Promise<string> {
    let html = `
<div style="text-align: center; margin-bottom: 6px;">
  <div style="font-size: ${fs.header}px; font-weight: bold;">RESTAURANT NAME</div>
  <div style="font-size: ${fs.normal - 1}px;">123 Restaurant St</div>
  <div style="font-size: ${fs.normal - 1}px;">Phone: (*************</div>
</div>

<div style="text-align: center; margin-bottom: 4px; border-bottom: 1px solid #000; padding-bottom: 2px;">
  <div style="font-size: ${fs.bold}px;">RECEIPT</div>
  <div style="font-size: ${fs.normal}px;">Order #${extractDailySequence(order.id)}</div>
  <div style="font-size: ${fs.normal - 1}px;">${new Date(order.createdAt).toLocaleString()}</div>
</div>
`;

    // Items section - list each item individually
    html += `<div style="margin-bottom: 4px;">`;
    order.items.forEach(item => {
      // Render each item individually based on quantity
      for (let i = 0; i < item.quantity; i++) {
        html += `
<div style="display: flex; justify-content: space-between; font-size: ${fs.normal}px; margin-bottom: 1px;">
  <span>${item.name}${item.size ? ` (${item.size})` : ''}</span>
  <span>${item.price.toFixed(2)} DA</span>
</div>`;
      }
      
      if (item.addons && item.addons.length > 0) {
        item.addons.forEach(addon => {
          html += `
<div style="display: flex; justify-content: space-between; font-size: ${fs.normal - 1}px; margin-left: 8px; margin-bottom: 1px;">
  <span>+ ${addon.name}</span>
  <span>${addon.price.toFixed(2)} DA</span>
</div>`;
        });
      }
    });
    html += `</div>`;

    // Total section
    html += `
<div style="border-top: 1px solid #000; padding-top: 2px; margin-top: 4px;">
  <div style="display: flex; justify-content: space-between; font-size: ${fs.bold}px; font-weight: bold;">
    <span>TOTAL:</span>
    <span>${order.total.toFixed(2)} DA</span>
  </div>
</div>
`;

    // Customer info for delivery orders
    if (order.customer && (order.customer.phone || order.customer.address)) {
      html += `
<div style="border-top: 1px dashed #000; padding-top: 4px; margin-top: 4px; font-size: ${fs.normal}px;">
  <div style="font-weight: bold; margin-bottom: 2px;">CUSTOMER INFO:</div>
  ${order.customer.phone ? `<div>Phone: ${order.customer.phone}</div>` : ''}
  ${order.customer.address ? `<div>Address: ${order.customer.address}</div>` : ''}
</div>
`;
    }

    // Payment info
    if (payment) {
      html += `
<div style="margin-top: 4px; font-size: ${fs.normal}px;">
  <div>Payment Method: ${payment.method.toUpperCase()}</div>
  <div>Amount Received: ${payment.received.toFixed(2)} DA</div>
  <div>Change: ${payment.change.toFixed(2)} DA</div>
</div>
`;
    }

    html += `
<div style="text-align: center; margin-top: 6px; font-size: ${fs.normal - 1}px;">
  <div>Thank you for your visit!</div>
  <div>Please come again!</div>
</div>
`;

    return await this.generatePrintHtmlWrapper('receipt', order, fs, html, { tableId, receiptPaymentInfo: payment });
  }

  // 🎟️ Generate Single System Print Ticket - COMPACT
  private async generateSingleSystemTicket(
    order: Order, 
    tableId?: string, 
    options: { fontSize?: 'small' | 'medium' | 'large' } = {}
  ): Promise<string> {
    const fs = this.getFontSizes(options.fontSize);
    
    // 🎯 USE THE NEW COMPACT SINGLE SYSTEM CONTENT
    const mainContent = await this.generateSingleSystemContent(order, fs, tableId);
    
    // 🎯 USE THE NEW COMPACT WRAPPER
    return this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: 'KITCHEN' });
  }

  // 🎟️ Generate Multi-Station Print Ticket (for actual printing)
    private async generateMultiStationTicket(
    order: Order,
    stationItems: OrderItem[],
    printer: PrinterConfig,
    allStationItems: Record<string, OrderItem[]>,
    tableId?: string,
    options: { fontSize?: 'small' | 'medium' | 'large' } = {}
  ): Promise<string> {
    const fs = this.getFontSizes(options.fontSize);
    const categoryId = printer.assignedCategories[0] || printer.name.replace(/ Printer$/, '');
    
    // 🎯 FIX: Get actual category name instead of ID
    let categoryName = categoryId.toUpperCase();
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      if (category) {
        categoryName = category.name.toUpperCase();
      }
    } catch (error) {
      console.warn('⚠️ Could not load menu for category name:', error);
    }
    
    // 🎯 USE THE SAME COMPACT FORMAT AS OTHER FUNCTIONS
    let mainContent = `<div style="font-size: ${fs.bold}px; font-weight: bold; margin-bottom: 2px;">${categoryName}:</div>`;
    
    // 🎯 COMPACT ITEM LISTING - NO BOXES, DIRECT LIST
    stationItems.forEach(item => {
      mainContent += `<div style="margin-bottom: 2px; margin-left: 4px;">${this.renderKitchenItem(item, fs)}</div>`;
    });

    // 🎯 COMPACT QUEUE DISPLAY: Show other stations with minimal space
    const otherCategoriesInfo = await this.getOtherCategoriesInfo(allStationItems, printer.id);
    console.log(`🖨️ [generateMultiStationTicket] Other categories info for ${printer.name}:`, otherCategoriesInfo);

    if (otherCategoriesInfo.length > 0) {
      const hasQueueItems = otherCategoriesInfo.some(info => info.queueCount > 0);
      
      if (hasQueueItems) {
        mainContent += `<div style="margin-top: 4px; padding-top: 2px; border-top: 1px solid #000;">`;
        
        otherCategoriesInfo.forEach(info => {
          if (info.queueCount > 0) {
            mainContent += `<div style="font-size: ${fs.normal - 1}px; margin-bottom: 1px;">${info.name.toUpperCase()}: ${info.queueCount}</div>`;
          }
        });
        
        mainContent += `</div>`;
      }
    }
    
    // 🎯 USE THE NEW COMPACT WRAPPER
    return this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: categoryName });
  }

  // 🎟️ Generate Barcode Station Print Ticket - ULTRA COMPACT
  private async generateBarcodeStationTicket(
    order: Order,
    stationItems: OrderItem[],
    printer: PrinterConfig,
    allStationItems: Record<string, OrderItem[]>,
    tableId?: string,
    options: { fontSize?: 'small' | 'medium' | 'large' } = {}
  ): Promise<string> {
    const fs = this.getFontSizes(options.fontSize);
    const categoryId = printer.assignedCategories[0] || printer.name.replace(/ Printer$/, '');
    
    // 🎯 FIX: Get actual category name instead of ID
    let categoryName = categoryId.toUpperCase();
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      if (category) {
        categoryName = category.name.toUpperCase();
      }
    } catch (error) {
      console.warn('⚠️ Could not load menu for category name:', error);
    }
    
    // 🎯 COMPACT HEADER: Category directly
    let mainContent = `<div style="font-size: ${fs.bold}px; font-weight: bold; margin-bottom: 2px;">${categoryName}:</div>`;
    
    for (const stationItem of stationItems) {
      for (let i = 0; i < stationItem.quantity; i++) {
        // 🎯 CRITICAL FIX: Calculate exact global position for this specific item instance
        let globalItemIndex = 1;
        
        // Count all item instances that come before this one in the order
        for (const orderItem of order.items) {
          if (orderItem.id === stationItem.id) {
            // Found our item, add the current instance index
            globalItemIndex += i;
            break;
          }
          // Add all quantities from items that come before this one
          globalItemIndex += orderItem.quantity;
        }
        
        // 🎯 Use exact global index for this item instance
        const simpleBarcodeId = this.generateSimpleBarcodeId(order.id, globalItemIndex);
        
        // 🎯 Generate barcode SVG and embed it directly
        let barcodeSvg = '';
        try {
          const barcodeDataURL = barcodeService.generateKitchenBarcodeDataURL(simpleBarcodeId);
          barcodeSvg = `<img src="${barcodeDataURL}" style="width: 100%; max-width: 180px; height: 25px; margin: 1px 0;" alt="" />`;
          console.log(`✅ [generateBarcodeContent] Barcode generated successfully for ${simpleBarcodeId}, data URL length: ${barcodeDataURL.length}`);
        } catch (error) {
          console.error('❌ Error generating barcode:', error);
          barcodeSvg = `<div style="text-align: center; font-size: 9px; border: 1px solid #000; padding: 1px;">${simpleBarcodeId}</div>`;
        }
        
        // 🎯 ULTRA COMPACT BARCODE ITEM
        mainContent += `<div style="margin-bottom: 2px; padding: 1px; border: 1px solid #000; margin-left: 4px;">`;
        mainContent += this.renderKitchenItem(stationItem, fs);
        mainContent += `<div style="text-align: center; margin: 1px 0;">${barcodeSvg}</div></div>`;
      }
    }

    // 🎯 COMPACT QUEUE DISPLAY: Show other stations with minimal space
    const otherCategoriesInfo = await this.getOtherCategoriesInfo(allStationItems, printer.id);
    console.log(`🖨️ [generateBarcodeStationTicket] Other categories info for ${printer.name}:`, otherCategoriesInfo);

    if (otherCategoriesInfo.length > 0) {
      const hasQueueItems = otherCategoriesInfo.some(info => info.queueCount > 0);
      
      if (hasQueueItems) {
        mainContent += `<div style="margin-top: 4px; padding-top: 2px; border-top: 1px solid #000;">`;
        
        otherCategoriesInfo.forEach(info => {
          if (info.queueCount > 0) {
            mainContent += `<div style="font-size: ${fs.normal - 1}px; margin-bottom: 1px;">${info.name.toUpperCase()}: ${info.queueCount}</div>`;
          }
        });
        
        mainContent += `</div>`;
      }
    }
    
    // Pass through the new compact wrapper
    return this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: categoryName });
  }

  private initializeItemStatuses(order: Order, stationItems: OrderItem[], stationId: string) {
    // Initialize status for each item in the order, specific to a station
    order.items.forEach(item => {
      if (item.categoryId && stationItems.some(si => si.id === item.id)) {
        const existingStatus = this.itemStatuses.get(item.id);
        if (!existingStatus) {
          this.itemStatuses.set(item.id, {
        orderId: order.id,
            itemId: item.id,
        itemName: item.name,
        status: 'pending',
            stationId: stationId,
        createdAt: new Date().toISOString()
      });
        }
      }
    });
  }

  scanItemBarcode(barcode: string): { success: boolean; message: string; shouldPrintExpo?: boolean } {
    // Direct lookup – key is the barcode itself
    const itemStatus = this.itemStatuses.get(barcode);

    if (!itemStatus) {
      return { success: false, message: 'Item not found or already completed.' };
    }

    // Mark as done
    itemStatus.status = 'done';
    itemStatus.scannedAt = new Date().toISOString();
    this.itemStatuses.set(barcode, itemStatus); // Update map

    // Check if all items for this order are done
    const allOrderItems = Array.from(this.itemStatuses.values()).filter(status => status.orderId === itemStatus.orderId);
    const pendingItemsCount = allOrderItems.filter(status => status.status === 'pending').length;

      return { 
        success: true, 
      message: `Item ${itemStatus.itemName} marked as done.`,
      shouldPrintExpo: pendingItemsCount === 0 // If no pending items, suggest printing expo ticket
    };
  }
  
  generateExpoTicket(orderId: string): PrintJob | null {
    const orderItems = Array.from(this.itemStatuses.values())
      .filter(status => status.orderId === orderId);
    
    if (!orderItems.every(status => status.status === 'done')) {
      return null;
    }
    
    const content = `
      <div class="text-base font-mono leading-tight">
        <div class="text-center font-bold text-xl mb-4 border-b-2 border-black pb-2">EXPO STATION</div>
        <div class="text-center font-bold text-lg mb-2 bg-green-200 p-2">READY FOR ASSEMBLY</div>
        <div class="text-center font-bold text-lg mb-2">ORDER #${orderId}</div>
        <div class="text-center text-sm mb-4">${new Date().toLocaleString()}</div>
        <hr class="my-2">
        
        <div class="mb-4 bg-green-50 p-3 border-2 border-green-500">
          <div class="font-bold mb-2 text-center">ALL STATION ITEMS COMPLETED</div>
          <div class="text-sm text-center">All kitchen stations have finished their items.</div>
          <div class="text-sm text-center font-bold">Order is ready for final assembly and service.</div>
        </div>
        
        <div class="mb-4">
          <div class="font-bold mb-2 border-b">COMPLETED ITEMS SUMMARY:</div>
          <div class="text-sm">Total items scanned: ${orderItems.length}</div>
          <div class="text-sm">All barcodes verified: YES</div>
          <div class="text-sm">Status: READY TO SERVE</div>
        </div>
        
        <hr class="my-2">
        <div class="text-center font-bold text-lg bg-green-600 text-white p-3">
          SERVE IMMEDIATELY
        </div>
        <div class="text-center text-xs mt-2">PRINTED: ${new Date().toLocaleString()}</div>
      </div>
    `;
    
    const dailySequence = extractDailySequence(orderId);
    return {
      title: `EXPO - ORDER #${dailySequence} READY`,
      content,
      type: 'expo'
    };
  }
  
  getOrderStatus(orderId: string): { pending: number; done: number; total: number } {
    const orderItems = Array.from(this.itemStatuses.values())
      .filter(status => status.orderId === orderId);
    
    const pending = orderItems.filter(status => status.status === 'pending').length;
    const done = orderItems.filter(status => status.status === 'done').length;
    
    return { pending, done, total: orderItems.length };
  }
  
  async printReceipt(
    order: Order,
    payment: { method: string; received: number; change: number },
    options: { fontSize?: 'small' | 'medium' | 'large'; printerId?: string } = {}
  ): Promise<PrintResult> {
    const fontSize = options.fontSize || 'medium';
    const fontSizes = {
      small: { header: 14, normal: 10, bold: 12 },
      medium: { header: 16, normal: 12, bold: 14 },
      large: { header: 18, normal: 14, bold: 16 }
    };
    const fs = fontSizes[fontSize];

    const content = await this.generateReceiptContent(order, fs, undefined, payment);

    const dailySequence = extractDailySequence(order.id);
    const printJob: PrintJob = {
      title: `Receipt - Order #${dailySequence}`,
      content: content,
      type: 'receipt',
      printerId: options.printerId || this.getReceiptPrinterId() || undefined,
    };

    // 🖨️ EXECUTE ACTUAL RECEIPT PRINTING
    let actuallyPrinted = false;
    let printExecutionResults: any[] = [];
    let printSuccessRate = 0;

    try {
      console.log(`🖨️ [printReceipt] Executing receipt print for order ${order.id}`);

      // Convert to execution print job format
      const executionJob: ExecutionPrintJob = {
        id: `receipt-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
        title: printJob.title,
        content: printJob.content,
        type: 'receipt',
        printerName: this.getPrinterNameById(printJob.printerId),
        copies: 1,
        priority: 'normal',
        createdAt: new Date().toISOString()
      };

      // Execute receipt print immediately
      const printResult = await printExecutionService.executePrint(executionJob);
      printExecutionResults.push(printResult);
      actuallyPrinted = printResult.success;
      printSuccessRate = printResult.success ? 100 : 0;

      console.log(`🖨️ [printReceipt] Receipt print result:`, {
        success: printResult.success,
        printer: printResult.printerUsed,
        error: printResult.error
      });

    } catch (error) {
      console.error(`🖨️ [printReceipt] Failed to execute receipt print:`, error);
      printExecutionResults.push({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown print error'
      });
    }

    return {
      success: true,
      printJob: printJob,
      showPreview: shouldShowPrintPreview(actuallyPrinted, printJob !== null), // Use centralized logic
      actuallyPrinted,
      printExecutionResults,
      printSuccessRate
    };
  }
  
  async resetPrinters(): Promise<void> {
    await savePrinterSettings([]);
    this.printers = [];
    console.log('🖨️ Printers configuration reset.');
  }

  /**
   * Validate and fix printer category assignments
   * Ensures all assignedCategories contain only valid category names from the current menu
   */
  async validateCategoryAssignments(): Promise<{
    success: boolean;
    fixed: boolean;
    report: {
      validCategories: string[];
      invalidAssignments: Array<{ printerId: string; printerName: string; invalidCategories: string[] }>;
      fixedPrinters: Array<{ printerId: string; printerName: string; removedCategories: string[] }>;
    };
  }> {
    try {
      // Get current menu categories
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const validCategoryNames = menu.categories.map(c => c.name);
      
      const report = {
        validCategories: validCategoryNames,
        invalidAssignments: [] as Array<{ printerId: string; printerName: string; invalidCategories: string[] }>,
        fixedPrinters: [] as Array<{ printerId: string; printerName: string; removedCategories: string[] }>
      };

      let needsFixing = false;
      const fixedPrinters = this.printers.map(printer => {
        const invalidCategories = printer.assignedCategories.filter(catName => 
          !validCategoryNames.includes(catName)
        );
        
        if (invalidCategories.length > 0) {
          needsFixing = true;
          
          // Log invalid assignments
          report.invalidAssignments.push({
            printerId: printer.id,
            printerName: printer.name,
            invalidCategories
          });
          
          // Remove invalid categories
          const validAssignedCategories = printer.assignedCategories.filter(catName => 
            validCategoryNames.includes(catName)
          );
          
          report.fixedPrinters.push({
            printerId: printer.id,
            printerName: printer.name,
            removedCategories: invalidCategories
          });
          
          console.log(`🔧 [validateCategoryAssignments] Fixed printer "${printer.name}": removed invalid categories:`, invalidCategories);
          
          return {
            ...printer,
            assignedCategories: validAssignedCategories
          };
        }
        
        return printer;
      });

      if (needsFixing) {
        // Save the fixed printer settings
        await savePrinterSettings(fixedPrinters);
        this.printers = fixedPrinters;
        console.log('✅ [validateCategoryAssignments] Fixed printer category assignments');
      }

      return {
        success: true,
        fixed: needsFixing,
        report
      };
      
    } catch (error) {
      console.error('❌ [validateCategoryAssignments] Failed to validate printer categories:', error);
      return {
        success: false,
        fixed: false,
        report: {
          validCategories: [],
          invalidAssignments: [],
          fixedPrinters: []
        }
      };
    }
  }
  
  async forceRefreshPrinters(): Promise<void> {
    console.log('🔄 Forcing refresh of printers...');
    this.printers = []; // Clear existing printers
    await savePrinterSettings([]); // Clear from DB as well
    console.log('✅ Printers refreshed.');
  }

  // 🧹 Debug method to check and optionally reset queue
  async debugQueueStatus(): Promise<{
    stationQueues: Array<{
      stationId: string;
      stationName: string;
      totalPendingItems: number;
      queueItems: Array<{
        orderId: string;
        status: string;
        itemCount: number;
        createdAt: string;
      }>;
    }>;
    totalPendingItems: number;
  }> {
    console.log('🔍 [debugQueueStatus] Checking all station queues...');

    const stationQueues = [];
    let totalPendingItems = 0;

    for (const printer of this.printers) {
      try {
        const queueContext = await this.getStationQueueContext(printer.id);
        const { getStationQueue } = await import('@/lib/db/v4/operations/queue-ops');
        const stationQueue = await getStationQueue(printer.id);

        const queueInfo = {
          stationId: printer.id,
          stationName: printer.name,
          totalPendingItems: queueContext.totalPendingItems,
          queueItems: stationQueue.items.map(item => ({
            orderId: item.orderId,
            status: item.status,
            itemCount: item.items.reduce((sum, orderItem) => sum + orderItem.quantity, 0),
            createdAt: item.createdAt
          }))
        };

        stationQueues.push(queueInfo);
        totalPendingItems += queueContext.totalPendingItems;

      } catch (error) {
        console.error(`❌ Error checking queue for station ${printer.id}:`, error);
      }
    }

    console.log('🔍 [debugQueueStatus] Queue summary:', {
      totalStations: stationQueues.length,
      totalPendingItems,
      stationBreakdown: stationQueues.map(s => ({ name: s.stationName, pending: s.totalPendingItems }))
    });

    return { stationQueues, totalPendingItems };
  }

  // 🧹 Reset all queues (for debugging)
  async resetAllQueues(): Promise<void> {
    console.log('🧹 [resetAllQueues] Resetting all kitchen queues...');
    try {
      const { kitchenQueueService } = await import('@/lib/services/kitchen-queue-service');
      await kitchenQueueService.resetAllQueues();
      console.log('✅ [resetAllQueues] All queues have been reset');
    } catch (error) {
      console.error('❌ [resetAllQueues] Error resetting queues:', error);
      throw error;
    }
  }



  // 🎯 TESTING: Simulate order routing to validate printer assignments
  async testOrderRouting(order: Order): Promise<{
    success: boolean;
    routingReport: { itemName: string; categoryId?: string; assignedPrinter?: string; error?: string }[];
    errors: string[];
  }> {
    const routingReport: { itemName: string; categoryId?: string; assignedPrinter?: string; error?: string }[] = [];
    const errors: string[] = [];

    try {
      for (const item of order.items) {
        const categoryName = (item as any).categoryName;
        
        if (!categoryName) {
          const error = `Item "${item.name}" has no categoryName`;
          routingReport.push({
            itemName: item.name,
            categoryId: undefined,
            error
          });
          errors.push(error);
          continue;
        }

        const assignedPrinter = this.printers.find(printer =>
          printer.assignedCategories.includes(categoryName)
        );

        if (assignedPrinter) {
          routingReport.push({
            itemName: item.name,
            categoryId: categoryName,
            assignedPrinter: assignedPrinter.name
          });
        } else {
          const error = `No printer assigned to category "${categoryName}" for item: ${item.name}`;
          routingReport.push({
            itemName: item.name,
            categoryId: categoryName,
            error
          });
          errors.push(error);
        }
      }

      return {
        success: errors.length === 0,
        routingReport,
        errors
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown routing error';
      return {
        success: false,
        routingReport,
        errors: [errorMsg]
      };
    }
  }
  
  async addOSPrinter(osPrinter: OSPrinterInput): Promise<{ success: boolean; message: string }> {
    const existing = this.printers.find(p => p.id === osPrinter.id);
    if (existing) {
      return { success: false, message: `Printer with ID ${osPrinter.id} already exists.` };
    }

      const newPrinter: PrinterConfig = {
        id: osPrinter.id,
        name: osPrinter.name,
      ipAddress: osPrinter.ipAddress,
      status: osPrinter.status || 'online',
      assignedCategories: [], // No categories assigned by default for OS printers
      type: osPrinter.type || 'thermal', // Default to thermal
      simulated: false, // These are real OS printers
    };

    this.printers.push(newPrinter);
    await this.setPrinters(this.printers);
    return { success: true, message: `Printer ${newPrinter.name} added.` };
  }

  async removePrinter(printerId: string): Promise<{ success: boolean; message: string }> {
    const initialLength = this.printers.length;
    this.printers = this.printers.filter(p => p.id !== printerId);
    if (this.printers.length < initialLength) {
      await this.setPrinters(this.printers);
      return { success: true, message: `Printer ${printerId} removed.` };
    }
    return { success: false, message: `Printer ${printerId} not found.` };
  }

  getPrinterStatus(): { features: PrintingFeatures; printers: PrinterConfig[]; totalPrinters: number } {
    return {
      features: this.printingFeatures,
      printers: this.printers,
      totalPrinters: this.printers.length,
    };
  }

  // Helper: build itemStatuses for multi-barcode system (one entry per item instance)
  private buildBarcodeItemStatuses(order: Order): void {
    // Clear previous statuses related to the same order to avoid duplication if re-printed
    for (const key of Array.from(this.itemStatuses.keys())) {
      if (key.startsWith(`${order.id}-`)) {
        this.itemStatuses.delete(key);
      }
    }

    let globalIndex = 1;
    order.items.forEach(item => {
      for (let i = 0; i < item.quantity; i++) {
        const barcodeId = this.generateSimpleBarcodeId(order.id, globalIndex);
        this.itemStatuses.set(barcodeId, {
          orderId: order.id,
          itemId: barcodeId, // Store the barcode itself for direct lookup
          itemName: item.name,
          status: 'pending',
          createdAt: new Date().toISOString()
        });
        globalIndex++;
      }
    });
  }

  // 🎯 VERIFICATION: Validate print job content and formatting
  async verifyPrintJob(printJob: PrintJob, features: PrintingFeatures): Promise<{
    isValid: boolean;
    issues: string[];
    contentAnalysis: {
      hasOrderNumber: boolean;
      hasItems: boolean;
      hasTimestamp: boolean;
      hasTableInfo: boolean;
      hasBarcodes: boolean;
      hasQueueInfo: boolean;
      lineCount: number;
      estimatedPrintTime: number; // in seconds
    };
  }> {
    const issues: string[] = [];
    const content = printJob.content;
    const lines = content.split('\n');

    // Basic content validation
    const hasOrderNumber = /order\s*#?\d+/i.test(content);
    const hasItems = /qty|quantity|\d+x/i.test(content);
    const hasTimestamp = /\d{1,2}:\d{2}|\d{4}-\d{2}-\d{2}/.test(content);
    const hasTableInfo = /table|table\s*#?\d+/i.test(content);
    const hasBarcodes = content.includes('|||') || content.includes('▌') || content.includes('<img'); // Barcode patterns
    const hasQueueInfo = /queue|pending|station/i.test(content);

    // Feature-specific validation
    if (features.barcodeEnabled && !hasBarcodes) {
      issues.push('Barcode feature enabled but no barcodes found in print job');
    }

    if (!features.barcodeEnabled && hasBarcodes) {
      issues.push('Barcode feature disabled but barcodes found in print job');
    }

    if (features.queueEnabled && printJob.stationName && !hasQueueInfo) {
      issues.push('Queue feature enabled but no queue coordination info found');
    }

    // Content quality checks
    if (!hasOrderNumber) {
      issues.push('Print job missing order number');
    }

    if (!hasItems) {
      issues.push('Print job missing item information');
    }

    if (!hasTimestamp) {
      issues.push('Print job missing timestamp');
    }

    if (lines.length < 5) {
      issues.push('Print job content seems too short');
    }

    if (lines.length > 100) {
      issues.push('Print job content seems excessively long');
    }

    // Estimate print time (rough calculation)
    const estimatedPrintTime = Math.ceil(lines.length / 10); // ~10 lines per second for thermal printers

    return {
      isValid: issues.length === 0,
      issues,
      contentAnalysis: {
        hasOrderNumber,
        hasItems,
        hasTimestamp,
        hasTableInfo,
        hasBarcodes,
        hasQueueInfo,
        lineCount: lines.length,
        estimatedPrintTime
      }
    };
  }

  // 🎯 COMPREHENSIVE FEATURE TEST: Test all aspects of the printing system with different feature combinations
  async comprehensiveFeatureTest(features: PrintingFeatures, testOrder: Order): Promise<{
    success: boolean;
    features: PrintingFeatures;
    printResult: PrintResult;
    validationResult: any;
    routingResult: any;
    printJobVerifications: any[];
    overallScore: number; // 0-100
    recommendations: string[];
  }> {
    const recommendations: string[] = [];
    let score = 0;

    try {
      // Set features
      this.setPrintingFeatures(features);

      // Test validation
      const validationResult = await this.validateCategoryAssignments();
      if (validationResult.success) score += 25;
      else recommendations.push('Fix category assignment issues');

      // Test routing
      const routingResult = await this.testOrderRouting(testOrder);
      if (routingResult.success) score += 25;
      else recommendations.push('Resolve item routing problems');

      // Test printing
      const printResult = await this.printKitchenOrder(testOrder, testOrder.tableId, { fontSize: 'medium' });
      if (printResult.success) score += 25;
      else recommendations.push('Fix print job generation issues');

      // Verify print jobs
      const printJobVerifications = [];
      const printJobs = printResult.printJobs || (printResult.printJob ? [printResult.printJob] : []);

      for (const printJob of printJobs) {
        const verification = await this.verifyPrintJob(printJob, features);
        printJobVerifications.push({
          printJob: printJob.title,
          ...verification
        });

        if (verification.isValid) score += Math.floor(25 / printJobs.length);
        else recommendations.push(`Improve print job quality for ${printJob.title}`);
      }

      // Feature-specific recommendations
      if (!features.queueEnabled && !features.barcodeEnabled && printJobs.length > 1) {
        recommendations.push('Consider using queue feature for better coordination with multiple printers');
      }

      if (features.barcodeEnabled && printJobs.length > 1) {
        recommendations.push('Barcode system is working well with multiple stations');
      }

      return {
        success: score >= 75,
        features,
        printResult,
        validationResult,
        routingResult,
        printJobVerifications,
        overallScore: Math.min(100, score),
        recommendations
      };
    } catch (error) {
      return {
        success: false,
        features,
        printResult: { success: false, error: error instanceof Error ? error.message : 'Unknown error' },
        validationResult: null,
        routingResult: null,
        printJobVerifications: [],
        overallScore: 0,
        recommendations: ['Feature test failed - check error logs']
      };
    }
  }

  /**
   * Print kitchen cancel ticket when order is cancelled
   */
  async printKitchenCancel(order: Order, reason?: string): Promise<PrintResult> {
    console.log(`🚫 [printKitchenCancel] Printing cancel ticket for order ${order.id}`);

    try {
      const enabledPrinters = this.printers.filter(printer => printer.enabled);
      if (enabledPrinters.length === 0) {
        console.warn('🚫 [printKitchenCancel] No enabled printers found');
        return { success: false, error: 'No enabled printers found' };
      }

      const dailySequence = extractDailySequence(order.id);
      const cancelTime = new Date().toLocaleString();

      const content = `
        <div class="text-base font-mono leading-tight">
          <div class="text-center font-bold text-xl mb-4 border-b-2 border-red-600 pb-2 bg-red-100">⚠️ ORDER CANCELLED ⚠️</div>
          <div class="text-center font-bold text-lg mb-2 bg-red-200 p-2">STOP PREPARATION</div>
          <div class="text-center font-bold text-lg mb-2">ORDER #${dailySequence}</div>
          <div class="text-center text-sm mb-4">${cancelTime}</div>
          <hr class="my-2">
          
          <div class="mb-4 bg-red-50 p-3 border-2 border-red-500">
            <div class="font-bold mb-2 text-center text-red-700">CANCEL ALL ITEMS</div>
            <div class="text-sm text-center">This order has been cancelled.</div>
            <div class="text-sm text-center font-bold">Stop all preparation immediately.</div>
          </div>

          ${reason ? `
          <div class="mb-4">
            <div class="font-bold mb-2 border-b">CANCELLATION REASON:</div>
            <div class="text-sm">${reason}</div>
          </div>
          ` : ''}

          <div class="mb-4">
            <div class="font-bold mb-2 border-b">ORIGINAL ITEMS:</div>
            ${order.items.map(item => 
              `<div class="text-sm mb-1">• ${item.quantity}x ${item.name}${item.size ? ` (${item.size})` : ''}${item.notes ? ` - ${item.notes}` : ''}</div>`
            ).join('')}
          </div>

          <hr class="my-2">
          <div class="text-center font-bold text-lg bg-red-600 text-white p-3">
            DO NOT PREPARE - CANCELLED
          </div>
          <div class="text-center text-xs mt-2">PRINTED: ${cancelTime}</div>
        </div>
      `;

      // Send cancel ticket to ALL enabled printers (all stations need to know)
      const printJobs: PrintJob[] = [];
      const printExecutionResults: any[] = [];
      let successfulPrints = 0;

      for (const printer of enabledPrinters) {
        const cancelJob: PrintJob = {
          title: `CANCEL ORDER #${dailySequence}`,
          content,
          type: 'kitchen',
          printerId: printer.id,
          stationName: printer.name
        };

        printJobs.push(cancelJob);

        // Execute print
        try {
          const executionJob: ExecutionPrintJob = {
            id: `cancel-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
            title: cancelJob.title,
            content: cancelJob.content,
            type: 'kitchen',
            printerName: printer.name,
            copies: 1,
            priority: 'high', // High priority for cancellations
            createdAt: new Date().toISOString()
          };

          const printResult = await printExecutionService.executePrint(executionJob);
          printExecutionResults.push({
            ...printResult,
            printerId: printer.id,
            printerName: printer.name
          });

          if (printResult.success) {
            successfulPrints++;
          }

        } catch (error) {
          console.error(`🚫 [printKitchenCancel] Failed to execute cancel print for ${printer.name}:`, error);
          printExecutionResults.push({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown print error',
            printerId: printer.id,
            printerName: printer.name
          });
        }
      }

      const printSuccessRate = printJobs.length > 0 ? (successfulPrints / printJobs.length) * 100 : 0;
      const actuallyPrinted = successfulPrints > 0;

      console.log(`🚫 [printKitchenCancel] Cancel print summary: ${successfulPrints}/${printJobs.length} successful`);

      return {
        success: true,
        printJobs: printJobs,
        showPreview: shouldShowPrintPreview(actuallyPrinted, printJobs.length > 0),
        actuallyPrinted,
        printExecutionResults,
        printSuccessRate
      };

    } catch (error) {
      console.error(`🚫 [printKitchenCancel] Error printing cancel ticket:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Print kitchen update ticket when order items are modified
   */
  async printKitchenUpdate(order: Order, updateType: 'items_changed' | 'customer_changed' = 'items_changed'): Promise<PrintResult> {
    console.log(`🔄 [printKitchenUpdate] Printing update ticket for order ${order.id}, type: ${updateType}`);

    try {
      const dailySequence = extractDailySequence(order.id);
      const updateTime = new Date().toLocaleString();

      // For item changes, split by station; for customer changes, send to all printers
      if (updateType === 'items_changed') {
        // Use existing station splitting logic but add "UPDATED" header
        const stationItems = await this.splitOrderByStation(order);
        const printJobs: PrintJob[] = [];
        const printExecutionResults: any[] = [];
        let successfulPrints = 0;

        for (const [stationName, items] of Object.entries(stationItems)) {
          if (items.length === 0) continue;

          const printer = this.printers.find(p => p.name === stationName && p.enabled);
          if (!printer) continue;

          // Generate standard kitchen ticket content but with UPDATED header
          let content: string;
          if (this.printingFeatures.queueEnabled) {
            // Generate multi-station style tickets with queue coordination
            const allStationItems = await this.splitOrderByStation(order);
            content = await this.generateMultiStationTicket(
              order, 
              items, 
              printer, 
              allStationItems, 
              order.tableId, 
              { fontSize: 'medium' }
            );
            // Modify the content to show "UPDATED ORDER" instead of regular header
            content = content.replace(
              /<div style="font-size: \d+px; font-weight: bold;">[^<]*<\/div>/, 
              `<div style="font-size: 16px; font-weight: bold; color: orange;">🔄 UPDATED ORDER</div>`
            );
          } else {
            // Generate simple tickets with "UPDATED ORDER" header
            content = await this.generateSimpleTicket(
              order,
              items,
              printer,
              order.tableId,
              { fontSize: 'medium' }
            );
            // Modify the content to show "UPDATED ORDER" instead of regular header
            content = content.replace(
              /<div style="font-size: \d+px; font-weight: bold;">[^<]*<\/div>/, 
              `<div style="font-size: 16px; font-weight: bold; color: orange;">🔄 UPDATED ORDER</div>`
            );
          }

          const updateJob: PrintJob = {
            title: `UPDATE ORDER #${dailySequence} - ${stationName}`,
            content,
            type: 'kitchen',
            printerId: printer.id,
            stationName: stationName
          };

          printJobs.push(updateJob);

          // Execute print
          try {
            const executionJob: ExecutionPrintJob = {
              id: `update-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
              title: updateJob.title,
              content: updateJob.content,
              type: 'kitchen',
              printerName: printer.name,
              copies: 1,
              priority: 'high', // High priority for updates
              createdAt: new Date().toISOString()
            };

            const printResult = await printExecutionService.executePrint(executionJob);
            printExecutionResults.push({
              ...printResult,
              printerId: printer.id,
              printerName: printer.name
            });

            if (printResult.success) {
              successfulPrints++;
            }

          } catch (error) {
            console.error(`🔄 [printKitchenUpdate] Failed to execute update print for ${printer.name}:`, error);
            printExecutionResults.push({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown print error',
              printerId: printer.id,
              printerName: printer.name
            });
          }
        }

        const printSuccessRate = printJobs.length > 0 ? (successfulPrints / printJobs.length) * 100 : 0;
        const actuallyPrinted = successfulPrints > 0;

        console.log(`🔄 [printKitchenUpdate] Update print summary: ${successfulPrints}/${printJobs.length} successful`);

        return {
          success: true,
          printJobs: printJobs,
          showPreview: shouldShowPrintPreview(actuallyPrinted, printJobs.length > 0),
          actuallyPrinted,
          printExecutionResults,
          printSuccessRate
        };

      } else {
        // For customer changes, send simple notification to all enabled printers
        const enabledPrinters = this.printers.filter(printer => printer.enabled);
        if (enabledPrinters.length === 0) {
          console.warn('🔄 [printKitchenUpdate] No enabled printers found');
          return { success: false, error: 'No enabled printers found' };
        }

        const content = `
          <div class="text-base font-mono leading-tight">
            <div class="text-center font-bold text-xl mb-4 border-b-2 border-blue-600 pb-2 bg-blue-100">📝 ORDER UPDATED</div>
            <div class="text-center font-bold text-lg mb-2 bg-blue-200 p-2">CUSTOMER INFO CHANGED</div>
            <div class="text-center font-bold text-lg mb-2">ORDER #${dailySequence}</div>
            <div class="text-center text-sm mb-4">${updateTime}</div>
            <hr class="my-2">
            
            <div class="mb-4 bg-blue-50 p-3 border-2 border-blue-500">
              <div class="font-bold mb-2 text-center text-blue-700">CUSTOMER INFORMATION UPDATED</div>
              <div class="text-sm text-center">Order delivery/customer details have been modified.</div>
              <div class="text-sm text-center font-bold">Check updated order details in system.</div>
            </div>

            ${order.customer ? `
            <div class="mb-4">
              <div class="font-bold mb-2 border-b">CUSTOMER INFO:</div>
              <div class="text-sm">Name: ${order.customer.name || 'N/A'}</div>
              ${order.customer.phone ? `<div class="text-sm">Phone: ${order.customer.phone}</div>` : ''}
              ${order.customer.address ? `<div class="text-sm">Address: ${order.customer.address}</div>` : ''}
            </div>
            ` : ''}

            <hr class="my-2">
            <div class="text-center font-bold text-lg bg-blue-600 text-white p-3">
              CONTINUE PREPARATION
            </div>
            <div class="text-center text-xs mt-2">PRINTED: ${updateTime}</div>
          </div>
        `;

        const printJobs: PrintJob[] = [];
        const printExecutionResults: any[] = [];
        let successfulPrints = 0;

        for (const printer of enabledPrinters) {
          const updateJob: PrintJob = {
            title: `UPDATE ORDER #${dailySequence}`,
            content,
            type: 'kitchen',
            printerId: printer.id,
            stationName: printer.name
          };

          printJobs.push(updateJob);

          // Execute print
          try {
            const executionJob: ExecutionPrintJob = {
              id: `update-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
              title: updateJob.title,
              content: updateJob.content,
              type: 'kitchen',
              printerName: printer.name,
              copies: 1,
              priority: 'normal',
              createdAt: new Date().toISOString()
            };

            const printResult = await printExecutionService.executePrint(executionJob);
            printExecutionResults.push({
              ...printResult,
              printerId: printer.id,
              printerName: printer.name
            });

            if (printResult.success) {
              successfulPrints++;
            }

          } catch (error) {
            console.error(`🔄 [printKitchenUpdate] Failed to execute update print for ${printer.name}:`, error);
            printExecutionResults.push({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown print error',
              printerId: printer.id,
              printerName: printer.name
            });
          }
        }

        const printSuccessRate = printJobs.length > 0 ? (successfulPrints / printJobs.length) * 100 : 0;
        const actuallyPrinted = successfulPrints > 0;

        console.log(`🔄 [printKitchenUpdate] Update print summary: ${successfulPrints}/${printJobs.length} successful`);

        return {
          success: true,
          printJobs: printJobs,
          showPreview: shouldShowPrintPreview(actuallyPrinted, printJobs.length > 0),
          actuallyPrinted,
          printExecutionResults,
          printSuccessRate
        };
      }

    } catch (error) {
      console.error(`🔄 [printKitchenUpdate] Error printing update ticket:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Print delivery driver slip with customer information
   */
  async printDeliverySlip(order: Order, deliveryPerson?: { name: string; phone: string }): Promise<PrintResult> {
    console.log(`🚚 [printDeliverySlip] Printing delivery slip for order ${order.id}`);

    try {
      const receiptPrinter = this.printers.find(p => p.isReceiptPrinter && p.enabled);
      if (!receiptPrinter) {
        console.warn('🚚 [printDeliverySlip] No enabled receipt printer found for delivery slip');
        return { success: false, error: 'No enabled receipt printer found' };
      }

      const dailySequence = extractDailySequence(order.id);
      const printTime = new Date().toLocaleString();
      const isPaid = order.paymentStatus === 'paid';
      const collectionAmount = order.collectionStatus?.expectedAmount || order.total;

      const content = `
        <div class="text-base font-mono leading-tight">
          <div class="text-center font-bold text-xl mb-4 border-b-2 border-green-600 pb-2">🚚 DELIVERY SLIP</div>
          <div class="text-center font-bold text-lg mb-2">ORDER #${dailySequence}</div>
          <div class="text-center text-sm mb-4">${printTime}</div>
          <hr class="my-2">

          ${order.customer ? `
          <div class="mb-4 bg-yellow-50 p-3 border-2 border-yellow-500">
            <div class="font-bold mb-2 text-center">📞 CUSTOMER DETAILS</div>
            <div class="text-sm font-bold">Name: ${order.customer.name || 'N/A'}</div>
            ${order.customer.phone ? `<div class="text-sm font-bold">Phone: ${order.customer.phone}</div>` : ''}
            ${order.customer.address ? `<div class="text-sm font-bold">Address: ${order.customer.address}</div>` : ''}
          </div>
          ` : ''}

          ${deliveryPerson ? `
          <div class="mb-4">
            <div class="font-bold mb-2 border-b">DELIVERY PERSON:</div>
            <div class="text-sm">Name: ${deliveryPerson.name}</div>
            <div class="text-sm">Phone: ${deliveryPerson.phone}</div>
          </div>
          ` : ''}

          <div class="mb-4">
            <div class="font-bold mb-2 border-b">ORDER ITEMS:</div>
            ${order.items.map(item => {
              const addonsText = item.addons && item.addons.length > 0 
                ? ` + ${item.addons.map(addon => addon.name).join(', ')}` 
                : '';
              return `<div class="text-sm mb-1">${item.quantity}x ${item.name}${item.size ? ` (${item.size})` : ''}${addonsText}${item.notes ? ` - ${item.notes}` : ''}</div>`;
            }).join('')}
          </div>

          ${order.notes ? `
          <div class="mb-4">
            <div class="font-bold mb-2 border-b">ORDER NOTES:</div>
            <div class="text-sm">${order.notes}</div>
          </div>
          ` : ''}

          <hr class="my-2">
          
          <div class="mb-4 bg-gray-100 p-3">
            <div class="font-bold mb-2">PAYMENT STATUS:</div>
            ${isPaid ? 
              `<div class="text-lg font-bold text-green-700">✅ PAID - ${order.total} DA</div>
               <div class="text-sm">No collection required</div>` :
              `<div class="text-lg font-bold text-red-700">💰 COLLECT: ${collectionAmount} DA</div>
               <div class="text-sm">Payment to be collected on delivery</div>`
            }
          </div>

          <hr class="my-2">
          <div class="text-center font-bold text-lg bg-green-600 text-white p-3">
            FOR DELIVERY USE
          </div>
          <div class="text-center text-xs mt-2">PRINTED: ${printTime}</div>
        </div>
      `;

      const deliverySlipJob: PrintJob = {
        title: `DELIVERY SLIP - ORDER #${dailySequence}`,
        content,
        type: 'receipt',
        printerId: receiptPrinter.id
      };

      // Execute print
      let actuallyPrinted = false;
      let printExecutionResults: any[] = [];

      try {
        const executionJob: ExecutionPrintJob = {
          id: `delivery-slip-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
          title: deliverySlipJob.title,
          content: deliverySlipJob.content,
          type: 'receipt',
          printerName: receiptPrinter.name,
          copies: 1,
          priority: 'normal',
          createdAt: new Date().toISOString()
        };

        const printResult = await printExecutionService.executePrint(executionJob);
        printExecutionResults.push({
          ...printResult,
          printerId: receiptPrinter.id,
          printerName: receiptPrinter.name
        });

        actuallyPrinted = printResult.success;

        console.log(`🚚 [printDeliverySlip] Delivery slip print result:`, {
          success: printResult.success,
          printer: printResult.printerUsed,
          error: printResult.error
        });

      } catch (error) {
        console.error(`🚚 [printDeliverySlip] Failed to execute delivery slip print:`, error);
        printExecutionResults.push({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown print error',
          printerId: receiptPrinter.id,
          printerName: receiptPrinter.name
        });
      }

      return {
        success: true,
        printJob: deliverySlipJob,
        showPreview: shouldShowPrintPreview(actuallyPrinted, true),
        actuallyPrinted,
        printExecutionResults,
        printSuccessRate: actuallyPrinted ? 100 : 0
      };

    } catch (error) {
      console.error(`🚚 [printDeliverySlip] Error printing delivery slip:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// Export singleton instance
export const kitchenPrintService = new KitchenPrintService();
export default kitchenPrintService;
